import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HistoryModuleName, MESSAGES, dateFormat, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { Category, SpecificationMasterColumn } from '@pages/administration/pages/specification-configration/models/specification.model';
import { CategoryService } from '@pages/administration/pages/specification-configration/pages/category/category.service';
import { MakeModelService } from '@pages/administration/pages/specification-configration/pages/make-model/make-model.service';
import { UnitTypeService } from '@pages/administration/pages/specification-configration/pages/unit-type/unit-type.service';
import { PrivilegeActionResponseDTOs } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { ColumnDropdownService } from '@pages/common-table-column/column-dropdown/column-dropdown.service';
import { ColumnItem, FilterList, FilterModuleName } from '@pages/common-table-column/models/common-table.column.model';
import { InventoryMatchedParams, QuotationResponse, QuotationUnitDTOS } from '@pages/crm/models/customer-inventory.model';
import { Specifications } from '@pages/crm/models/customer-lead-quotation.model';
import { ExcelData, FilterDataWithCounts, IdNameCount, InventoryGeneralDetails, InventoryListFilter, InventoryListGlobalFilter, InventoryListItem, InventoryListingSpecificationData, InventorySpecification, InventorySpecificationResponse, InventorySpecificationSearchDTO, InventorySpecificationSearchObj, InventoryStatus, SpecificationField, Tabs, UnitType } from '@pages/inventory/models';
import { InventorySpecificationService } from '@pages/inventory/services';
import { GeneralInfoService } from '@pages/inventory/services/general-info.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import * as saveAs from 'file-saver';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { ConfirmationService, MenuItem, PrimeNGConfig } from 'primeng/api';
import { Observable, Subject, Subscription, debounceTime, forkJoin, map, of, takeUntil } from 'rxjs';
import { DataType, IdNameModel, OperatorType, TreeOperatorType } from 'src/app/@shared/models';
import { Utils } from 'src/app/@shared/services';
import { ColumnMasterService } from 'src/app/@shared/services/column-master.service';
import { CommonService } from 'src/app/@shared/services/common.service';

@Component({
  selector: 'app-inventory-list',
  templateUrl: './inventory-list.component.html',
  styleUrls: ['./inventory-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe]
})
export class InventoryListComponent extends BaseComponent implements OnInit, AfterViewInit {
  @Input() activeIndexes!: number;
  @Input()
  get selectedColumns(): ColumnItem[] {
    return this._selectedColumns;
  }

  set selectedColumns(val: any[]) {
    this._selectedColumns = this.cols.filter(col => val.includes(col));
  }
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() showSoldTab: EventEmitter<boolean> = new EventEmitter<boolean>();
  categorySpecification!: Array<InventorySpecification>;
  showInventoryDetails = false;
  myPreference: ColumnItem[] = [];
  isCategoryTab = false;
  hasRecentData = false;
  activeIndex!: number;
  activeTabIndex = 0;
  thisRecord = 'this {record}'
  recentlyAdded = false;
  cols: any[] = [];
  _selectedColumns: any[] = [];
  private listingSubscription!: Subscription;
  inventories: InventoryListItem[] = [];
  selectedInventories: InventoryListItem[] = [];
  filterParams: InventoryListFilter = new InventoryListFilter();
  createdDate!: Date | null;
  purchaseDate!: Date | null;

  // Range filter properties
  yearRange: Date[] | null = null;

  // Odometer range filter
  showOdometerRangeModal = false;
  odometerRangeStart: number | null = null;
  odometerRangeEnd: number | null = null;
  tempOdometerRangeStart: number | null = null;
  tempOdometerRangeEnd: number | null = null;

  // Price range filter
  showPriceRangeModal = false;
  currentPriceField: string = '';
  priceRanges: { [key: string]: { start: number | null, end: number | null } } = {
    retailAskingPrice: { start: null, end: null },
    initialInvestment: { start: null, end: null },
    actualInvestment: { start: null, end: null },
    totalProjectedInvestment: { start: null, end: null }
  };
  tempPriceRangeStart: number | null = null;
  tempPriceRangeEnd: number | null = null;
  showCreateModal = false;
  showInventoryDetailsModal = false;
  showHistoryModal = false;
  historyModuleName = HistoryModuleName.INVENTORY;
  showSoldModal = false;
  showDuplicateInventoryModal = false;
  inventoryStatus!: number | null;
  selectedInventory!: InventoryListItem | null;
  selectedInventoryContext!: InventoryListItem | null;
  selectedStatus!: number | null;
  isActiveTab = true;
  isArchiveInProgress = false;
  isEditMode = false;
  isViewMode = false;
  inventoryStatuses: InventoryStatus[] = [];
  showSoldTabs = false;
  showHoldTabs = false;
  showColumnModal = false;
  appliedFilterList!: FilterList | null;
  appliedPreference: ColumnItem[] = [];
  showPreferenceModal = false;
  showPreferenceListModal = false;
  showInventoryToBeSoldedModal = false;
  showInventoryToBeHoldModal = false;
  defaultTabs!: FilterList[];
  dropDownColumnList: ColumnItem[] = [];
  defaultDropDownColumnList: ColumnItem[] = [];
  specificationDropDownColumnList: ColumnItem[] = [];
  tableColumn!: FilterList | undefined;
  specificationColumn!: FilterList | undefined;
  userSelectedDefaultColumnsArray: ColumnItem[] = [];
  userSelectedSpecificationColumnsArray: ColumnItem[] = [];
  userSelectedColumnsArray: ColumnItem[] = [];
  globalSearch = new Subject<InventoryListFilter>();
  specificationSearchItems: any = {};
  hideFieldForSearch: string | null = '';
  initialFilterParams!: InventoryListFilter;
  recentlyAddedFilter!: FilterList | undefined;
  categoryTypes!: Array<Category>;
  makes!: Array<IdNameCount>;
  models!: Array<IdNameCount>;
  unitTypes!: Array<IdNameCount>;
  categoryTypesOptions!: Array<IdNameCount>;
  inventoryStatusesWithCount!: Array<IdNameCount>;
  createdByUsers!: Array<IdNameCount>;
  selectedCategoryId!: number | undefined;
  makeIds!: Array<number>;
  modelIds!: Array<number>;
  unitTypeIds!: Array<number>;
  userIds!: Array<number>;
  userPermissions!: PrivilegeActionResponseDTOs[];
  currentLocations: IdNameCount[] = [];
  receivingLocations: IdNameCount[] = [];
  displayOnWebOptions: IdNameCount[] = [];
  salesPersonOptions: IdNameModel[] = [];
  internetGroupOptions: IdNameModel[] = [];
  selectedQuotation!: QuotationResponse;
  isLoadingData = false;
  filterDataWithCounts!: FilterDataWithCounts;
  private lastContainerWidths = new Map<HTMLElement, number>();
  private windowResizeHandler = () => {
    setTimeout(() => {
      this.updateTagVisibility();
    }, 100);
  };
  excelDownloadMenuItems: MenuItem[] = [
    {
      label: "Selected Columns",
      command: () => this.exportUsersToExcel()
    },
    {
      label: "All Columnns",
      command: () => this.exportUsersToExcel(true)
    }
  ];
  selectedTabName!: string | undefined;

  constructor(private readonly inventoryService: InventoryService,
    private readonly commonSharedService: CommonSharedService,
    private readonly cdf: ChangeDetectorRef,
    private readonly toasterService: AppToasterService,
    private readonly confirmationService: ConfirmationService,
    private readonly authService: AuthService,
    private readonly inventorySpecificationService: InventorySpecificationService,
    private readonly primengConfig: PrimeNGConfig,
    readonly columnDropDownService: ColumnDropdownService,
    private readonly datePipe: DatePipe,
    private readonly userAnnotationService: UserAnnotationService,
    private readonly activeRoute: ActivatedRoute,
    private readonly makeModelService: MakeModelService,
    private readonly unitTypeService: UnitTypeService,
    private readonly columnMasterService: ColumnMasterService,
    private readonly commonService: CommonService,
    private readonly generalInfoService: GeneralInfoService,
    private readonly categoryService: CategoryService,
    private readonly dealerService: DealerService,

  ) {
    super();
    this.pageTitle = 'Inventory';
    this.paginationConfig.predicate = 'gi.stock_number';
    this.filterParams.orderBy = 'gi.stock_number';
    this.primengConfig.ripple = true;
    this.paginationConfig.itemsPerPage = this.paginationConfig.itemsPerPageOptions[1];
  }

  async ngOnInit() {
    this.getInventoryStatuses();

    await this.getCurrentUser();
    this.getCategoryTypes();
    this.getRecentCount();
    this.getFilterSaveParams();
    this.displaySearchResult();
    this.getInternetGroupList();
    this.getAll();
    this.inventoryService.imageUploaded.asObservable().subscribe({
      next: (response: boolean) => {
        if (response) {
          this.getAll();
        }
      }
    })
    this.initialFilterParams = { ...this.filterParams };
    this.activeRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.getDriverScheduleDetails(params.id);
        }
      })
    this.userPermissions = this.authService.getRoleInfo()?.privilegeActionResponseDTOs;
  }

  ngAfterViewInit(): void {
    // Set up PrimeNG table column resize listener
    this.setupPrimeNGColumnResizeListener();
    // Initial setup of tag visibility
    setTimeout(() => {
      this.updateTagVisibility();
    }, 2000);
  }

  private setupPrimeNGColumnResizeListener(): void {
    setTimeout(() => {
      let isResizing = false;

      // Only listen for mouse events on column resizers, not everywhere
      document.addEventListener('mousedown', (event) => {
        const target = event.target as HTMLElement;
        if (target && target.classList.contains('p-column-resizer')) {
          isResizing = true;
        }
      });

      document.addEventListener('mouseup', () => {
        if (isResizing) {
          isResizing = false;
          setTimeout(() => {
            this.updateTagVisibility();
          }, 150);
        }
      });

      // Window resize events
      window.addEventListener('resize', this.windowResizeHandler);
    }, 1000);
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    // Remove window resize listener
    window.removeEventListener('resize', this.windowResizeHandler);
  }

  private updateTagVisibility(): void {
    const internetGroupsCells = document.querySelectorAll('.internet-groups-cell');

    internetGroupsCells.forEach((cell) => {
      const container = cell.querySelector('.internet-groups-container') as HTMLElement;
      if (!container) return;

      const tags = container.querySelectorAll('.tag-item') as NodeListOf<HTMLElement>;
      const showMoreBadge = container.querySelector('.show-more-badge') as HTMLElement;

      if (tags.length === 0) return;

      // Reset: Show all tags, hide show more badge
      tags.forEach((tag) => {
        tag.style.display = 'inline-block';
      });

      if (showMoreBadge) {
        showMoreBadge.style.display = 'none';
        showMoreBadge.style.visibility = 'hidden';
      }

      // Get container dimensions
      const containerRect = container.getBoundingClientRect();
      const containerWidth = containerRect.width;

      // Check if width has actually changed to avoid unnecessary recalculations
      const lastWidth = this.lastContainerWidths.get(container);
      if (lastWidth === containerWidth) {
        return;
      }
      this.lastContainerWidths.set(container, containerWidth);

      // Check if we need to hide any tags
      setTimeout(() => {
        let totalWidth = 0;
        let visibleCount = 0;
        const showMoreWidth = 30; // Approximate width for show more badge

        // First, check if all tags fit without show more badge
        let allTagsWidth = 0;
        for (let i = 0; i < tags.length; i++) {
          const tag = tags[i] as HTMLElement;
          const tagRect = tag.getBoundingClientRect();
          const tagWidth = tagRect.width + 8; // Include margin
          allTagsWidth += tagWidth;
        }

        if (allTagsWidth <= containerWidth) {
          // All tags fit perfectly, no need for show more badge
          visibleCount = tags.length;
          // Ensure all tags are visible
          tags.forEach((tag) => {
            (tag as HTMLElement).style.display = 'inline-block';
          });
        } else {
          // Not all tags fit, calculate how many can fit with show more badge
          for (let i = 0; i < tags.length; i++) {
            const tag = tags[i] as HTMLElement;
            const tagRect = tag.getBoundingClientRect();
            const tagWidth = tagRect.width + 8; // Include margin

            // Check if this tag fits with show more badge
            const requiredWidth = totalWidth + tagWidth + showMoreWidth;

            if (requiredWidth <= containerWidth) {
              totalWidth += tagWidth;
              visibleCount++;
            } else {
              // Hide this and all remaining tags
              for (let j = i; j < tags.length; j++) {
                (tags[j] as HTMLElement).style.display = 'none';
              }
              break;
            }
          }
        }

        // Show/hide the show more badge
        if (showMoreBadge) {
          if (visibleCount < tags.length) {
            // Some tags are hidden, show the "..." badge
            showMoreBadge.style.display = 'inline-flex';
            showMoreBadge.style.visibility = 'visible';
            this.updateTooltipForCell(container, visibleCount);
          } else {
            // All tags are visible, hide the "..." badge
            showMoreBadge.style.display = 'none';
            showMoreBadge.style.visibility = 'hidden';
          }
        }
      }, 100);
    });
  }

  private updateTooltipForCell(container: HTMLElement, visibleCount: number): void {
    // Find the inventory data for this row
    const row = container.closest('tr');
    if (!row) return;

    const rowIndex = Array.from(row.parentElement?.children || []).indexOf(row);
    if (rowIndex >= 0 && this.inventories[rowIndex]) {
      const inventory = this.inventories[rowIndex];
      const showMoreBadge = container.querySelector('.show-more-badge') as HTMLElement;
      if (showMoreBadge && inventory.internetGroups) {
        // Update the tooltip content
        const tooltipContent = this.getRemainingBadgesTemplate(inventory.internetGroups, visibleCount);
        showMoreBadge.setAttribute('pTooltip', tooltipContent);
      }
    }
  }


  getDriverScheduleDetails(id: string): void {
    this.inventoryService.get<InventoryListItem>(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: InventoryListItem) => {
          this.onViewEditClick(res);
        }
      });
  }

  onPageChanged($event: PageChangedEvent): void {
    this.getAll();
  }

  onTabChanged(tabChangeEvent: any, preference: ColumnItem[] = [], isApplyPreference = false): void {
    this.commonSharedService.setBlockUI$(true);
    this.activeTabIndex = tabChangeEvent.index;
    this.myPreference = [];
    this.categorySpecification = [];
    this.specificationSearchItems = {};
    this.recentlyAdded = false;
    this.dropDownColumnList = this.inventories = this.selectedInventories = [];
    this.appliedPreference = preference;
    const tab = this.defaultTabs[tabChangeEvent.index];
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.appliedFilterList = preference.length ? this.appliedFilterList : null;
    if (tab?.data) {
      const tabData = JSON.parse(tab?.data);
      this.filterParams = tabData;
      this.selectedTabName = tab.filterName;
      this.initialFilterParams = { ...this.filterParams };
      this.paginationConfig.predicate = 'gi.stock_number';
      this.filterParams.orderBy = 'gi.stock_number';
      this.filterParams.direction = "asc";
      this.hideFieldForSearch = tab.hideField ?? null;
      if (tab.hideField === "Category") {
        this.selectedCategoryId = Number(this.categoryTypes.find((category) => {
          return category.name === tab.filterName
        })?.id)
      } else {
        this.selectedCategoryId = undefined;
      }
    }
    this.makeIds = this.modelIds = this.unitTypeIds = this.userIds = [];
    this.makes = this.models = this.unitTypes = [];
    this.getInventoryStatuses();
    this.getCategoryTypes();
    if (!isApplyPreference) {
      this.getAll();
    }
    this.getInventorySpecificationsForm();
    this.checkIfActiveTabIsCategoryTab(isApplyPreference);
  }

  recentlyAddedData(tab?: FilterList) {
    this.recentlyAdded = true;
    setTimeout(() => {
      this.activeTabIndex = this.defaultTabs.length;
    }, 0);
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    if (tab?.data) {
      const tabData = JSON.parse(tab?.data);
      this.filterParams = tabData;
      this.filterParams.startDate = this.recentStartDate;
      this.filterParams.endDate = this.recentEndDate;
      this.initialFilterParams = { ...this.filterParams }
      this.hideFieldForSearch = tab.hideField ?? null;
    }
    this.getAll();
  }

  getAll(): void {
    if (this.isLoadingData) {
      return;
    }
    this.commonSharedService.setBlockUI$(true);
    this.isLoading = true;
    this.isLoadingData = true;
    this.inventoryService.getListWithFiltersWithPagination<InventoryListFilter, InventoryListItem>(
      this.filterParams,
      this.paginationConfig.page,
      this.paginationConfig.itemsPerPage,
      API_URL_UTIL.inventory.search
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (res) => {
        this.inventories = this.transformInventoryDto(res.content);
        this.setPaginationParamsFromPageResponse<InventoryListItem>(res);
        this.setActiveFlagForAll();
        this.getFilterDataWithCount();
        this.commonSharedService.setBlockUI$(false);
        this.isLoading = false;
        this.isLoadingData = false;
        this.cdf.detectChanges();
        // Update tag visibility after data is loaded and DOM is updated
        setTimeout(() => {
          this.updateTagVisibility();
        }, 100);
      },
      error: () => {
        this.commonSharedService.setBlockUI$(false);
        this.isLoading = false;
        this.isLoadingData = false;
        this.cdf.detectChanges();
      }
    });
  }


  private transformInventoryDto(inventories: InventoryListItem[]): InventoryListItem[] {
    if (!inventories.length) {
      return [];
    }
    return inventories.map(inventory => {
      const specificationDetails = this.getSpecificationDetails(inventory);
      inventory.specificationDetails = { ...specificationDetails };
      this.cdf.detectChanges();
      return inventory;
    });
  }

  private getSpecificationDetails(inventory: InventoryListItem): Record<string, Record<string, any>> {
    const specificationDetails: Record<string, Record<string, any>> = {};
    const specification = inventory?.unitSpecification?.specificationData?.specification || [];
    specification.forEach(section => {
      const parentKey = section.sectionName.replace(/ - \d+$/, '');
      if (!(section.parentName || section.multiple) || !specificationDetails[parentKey]) {
        specificationDetails[parentKey] = this.getChildKeyValues(section);
      } else if (section.parentName || section.multiple) {
        this.updateChildKeyValues(section, parentKey, specificationDetails);
      }
    });
    return specificationDetails;
  }

  private getChildKeyValues(section: InventoryListingSpecificationData): Record<string, any> {
    const childKeyValues: Record<string, any> = {};
    if (section?.fields?.length) {
      section.fields.forEach((field: SpecificationField) => {
        const label = field["label"];
        let value: string | number | null = null;
        if (field.dataType.toUpperCase() === 'DROPDOWN') {
          value = field.options.find((option: IdNameModel) => option.id === field["value"])?.name ?? null;
        } else {
          value = field["value"];
        }
        childKeyValues[label] = value;
      });
    }
    return { ...childKeyValues };
  }

  private updateChildKeyValues(section: InventoryListingSpecificationData, parentKey: string, specificationDetails: Record<string, Record<string, any>>): void {
    if (section?.fields?.length) {
      section.fields.forEach((field: SpecificationField) => {
        const label = field["label"];
        let value: string | number | null = null;
        if (field.dataType.toUpperCase() === 'DROPDOWN') {
          value = field.options.find((option: IdNameModel) => option.id === field["value"])?.name ?? null;
        } else {
          value = field["value"];
        }
        if (specificationDetails[parentKey][label]) {
          if (value) {
            specificationDetails[parentKey][label] = `${specificationDetails[parentKey][label]}, ${value}`
          }
        } else {
          specificationDetails[parentKey][label] = value;
        }
      });
    }
  }


  get recentStartDate(): string {
    const date = new Date();
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)).toISOString();
  }

  get recentEndDate(): string {
    const date = new Date();
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 50)).toISOString();
  }

  getRemainingBadgesTemplate(groups: any[], visibleCount: number = 2): string {
    if (!groups || groups.length <= visibleCount) {
      return '';
    }
    let tooltipContent = '<div style="display: flex; flex-direction: column; gap: 4px; width: 100%;">';
    const remainingGroups = groups.slice(visibleCount);
    for (let i = 0; i < remainingGroups.length; i += 2) {
      tooltipContent += '<div style="display: flex; flex-wrap: wrap; gap: 4px; margin: 0;">';
      tooltipContent += `<span class="tooltip-badge">${remainingGroups[i].internetGroupName}</span>`;
      if (i + 1 < remainingGroups.length) {
        tooltipContent += `<span class="tooltip-badge">${remainingGroups[i + 1].internetGroupName}</span>`;
      }
      tooltipContent += '</div>';
    }
    tooltipContent += '</div>';
    return tooltipContent;
  }

  getVisibleTagsCount(groups: any[], containerWidth: number): number {
    if (!groups || groups.length === 0) {
      return 0;
    }

    // Approximate width calculations
    const tagMargin = 8; // me-2 class margin
    const showMoreBadgeWidth = 30; // width of the "..." badge
    const averageTagWidth = 80; // estimated average width of a tag

    // Reserve space for the show more badge if there are more than 2 tags
    const availableWidth = groups.length > 2 ? containerWidth - showMoreBadgeWidth : containerWidth;

    // Calculate how many tags can fit
    const maxTags = Math.floor(availableWidth / (averageTagWidth + tagMargin));

    // Ensure at least 1 tag is shown, but not more than total available
    return Math.max(1, Math.min(maxTags, groups.length));
  }

  getRecentCount(): void {
    const recentFilter: InventoryListGlobalFilter = {
      archived: false,
      treeOperator: TreeOperatorType.NOOP,
      values: [{
        dataType: DataType.DATE,
        key: "createdDate",
        operator: OperatorType.GREATER_THAN_OR_EQUAL,
        value: this.recentStartDate
      }],
      orderBy: [{ ascending: true, field: "generalInformation.stockNumber" }]
    }
    this.inventoryService.getListWithFiltersWithPagination<InventoryListGlobalFilter, InventoryListItem>(recentFilter, 1, 1, API_URL_UTIL.inventory.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        if (data.content.length) {
          this.hasRecentData = true;
        } else {
          this.hasRecentData = false;
        }
        this.cdf.detectChanges();
      });
  }

  private setActiveFlagForAll() {
    this.inventories.forEach(inventory => inventory.status = this.isActiveTab);
  }

  onAddEditPopupClose(refreshList: boolean): void {
    this.showCreateModal = false;
    this.selectedInventory = null;
    if (refreshList) {
      this.getAll();
      this.getRecentCount();
    }
    this.showSoldTabs = false
    this.showHoldTabs = false
  }

  onAdd(): void {
    this.isViewMode = false;
    this.showCreateModal = true;
    this.cdf.detectChanges();
  }

  editFromDetails() {
    this.commonSharedService.setBlockUI$(true);
    setTimeout(() => {
      if (this.selectedInventory) {
        this.onEdit(this.selectedInventory);
      }
    }, 500);
  }

  onEdit(inventory: InventoryListItem): void {
    this.isViewMode = false;
    this.showCreateModal = true;
    this.activeIndex = 0;
    this.selectedInventory = inventory;
    if (this.selectedInventory.generalInformation.unitStatus.id === 2) {
      this.showSoldTabs = true;
    }
    if (this.selectedInventory.generalInformation.unitStatus.id === 12) {
      this.showHoldTabs = true;
    }
    this.cdf.detectChanges();
  }

  archiveSelected(event: Event) {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: this.activeTabIndex !== 1 ? MESSAGES.archiveWarning.replace(this.thisRecord, 'these inventories') : MESSAGES.unArchiveWarning.replace(this.thisRecord, 'these inventories'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        const endpoint = `${API_URL_UTIL.inventory.archiveArray}?archive=${this.activeTabIndex !== 1 ? true : false}`;
        const selectedInventoriesId = this.selectedInventories.map(inventory => inventory.id);
        this.inventoryService.update(selectedInventoriesId, endpoint)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.toasterService.success(
                this.activeTabIndex !== 1 ? MESSAGES.inventoryArchiveSuccess.replace('Inventory', 'Inventories') : MESSAGES.inventoryUnArchiveSuccess.replace('Inventory', 'Inventories')
              );
              this.getRecentCount();
              this.selectedInventories = [];
              this.getAll();
            },
            error: () => {
              this.getAll();
              this.selectedInventories = [];
            }
          });
      }
    });
  }

  onArchive(inventory: InventoryListItem, isActive: boolean): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: !inventory.archived ? MESSAGES.archiveWarning.replace('{record}', 'inventory') : MESSAGES.unArchiveWarning.replace('{record}', 'inventory'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.isArchiveInProgress = true;
        inventory.archived = isActive;
        this.onArchiveConfirmation(inventory);
      },
      reject: () => {
        this.getAll();
        this.cdf.detectChanges();
      }
    });
  }

  private onArchiveConfirmation(inventory: InventoryListItem): void {
    const endpoint = `${API_URL_UTIL.inventory.archiveArray}?archive=${!inventory.archived}`;
    this.inventoryService.update([inventory.id], endpoint)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(
            !inventory.archived ? MESSAGES.inventoryArchiveSuccess : MESSAGES.inventoryUnArchiveSuccess
          );
          this.isArchiveInProgress = false;
          this.selectedInventories = this.selectedInventories.filter(data => data.id !== inventory.id);
          this.getRecentCount();
          this.getAll();
        },
        error: () => {
          this.isArchiveInProgress = false;
          this.getRecentCount();
          this.getAll();
        }
      });
  }

  deleteSelected(event: Event) {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace(this.thisRecord, 'these inventory data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        const selectedInventoriesId = this.selectedInventories.map(inventory => inventory.id);
        this.inventoryService.bulkDelete(selectedInventoriesId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('this {item}', 'these Inventories'));
              this.selectedInventories = [];
              this.getAll();
            }
          });
      }
    });
  }

  onDelete(inventory: InventoryListItem, event: Event): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'inventory data'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(inventory);
      }
    });
  }

  onDeleteConfirmation(inventory: InventoryListItem): void {
    this.inventoryService.bulkDelete([inventory.id])
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.deleteSuccessMessage.replace('{item}', 'Inventory'));
          this.selectedInventories = this.selectedInventories.filter(data => data.id !== inventory.id);
          this.getAll();
        }
      });
  }

  private getInventoryStatuses(): void {
    const endpoint = `${API_URL_UTIL.inventory.statuses}/${API_URL_UTIL.inventory.count}?archived=${this.isArchived()}`;
    this.inventoryService.getList<InventoryStatus>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (inventoryStatuses) => {
        this.inventoryStatuses = inventoryStatuses;
      },
    });
  }

  saveClickedInventoryStatus(statusId: number): void {
    this.inventoryStatus = statusId;
  }

  changeStatus(status: number, id: number, inventory: InventoryListItem): void {
    if (this.inventoryStatus === 2 && status !== 1) {
      this.toasterService.error(MESSAGES.onlyToAvailable);
      setTimeout(() => {
        inventory.generalInformation.unitStatus.id = 2;
      }, 0);
      return;
    }
    if (this.inventoryStatus === 12 && (status !== 1 && status !== 2)) {
      this.toasterService.error(MESSAGES.onlyToAvailableOrSold);
      setTimeout(() => {
        inventory.generalInformation.unitStatus.id = 12;
      }, 0);
      return;
    }
    if (status === 2) {
      if (!inventory.isFinancialAdded) {
        setTimeout(() => {
          inventory.generalInformation.unitStatus.id = Number(this.inventoryStatus);
        }, 0);
        this.toasterService.error(MESSAGES.fillFinancial);
        return;
      }
      this.showInventoryToBeSoldedModal = true;
      this.selectedInventory = inventory;
      this.selectedStatus = status;
      return;
    }
    if (status === 12) {
      this.showInventoryToBeHoldModal = true;
      this.selectedInventory = inventory;
      this.selectedStatus = status;
      return;
    }

    this.updateInventryStatus(status, id, inventory);
  }

  updateInventryStatus(status: number, id: number, inventory: InventoryListItem) {
    const endpoint = `status/${status}`;
    const queryParamas = `${endpoint}`
    this.inventoryService.update([id], queryParamas).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.toasterService.success(MESSAGES.statusChangeSuccess);
        this.inventoryStatus = null;
        this.getAll();
        if (status === 2) {
          this.showCreateModal = true;
          this.selectedInventory = inventory;
          this.activeIndex = Tabs.SALEINFORMATION;
          this.showSoldTabs = true;
          this.isViewMode = false;
        }
        if (status === 12) {
          this.showCreateModal = true;
          this.selectedInventory = inventory;
          this.activeIndex = Tabs.HOLD;
          this.showHoldTabs = true;
          this.isViewMode = false;
        }
        else {
          this.activeIndex = Tabs.GENERAL;
          this.showSoldTabs = false;
          this.showHoldTabs = false;
        }
      }
    });
  }

  onSoldModalClose(isClose: boolean): void {
    this.showInventoryToBeSoldedModal = false;
    this.getAll();
    if (isClose) {
      this.showCreateModal = true;
      this.selectedInventory = this.selectedInventory;
      this.activeIndex = Tabs.SALEINFORMATION;
      this.showSoldTabs = true;
      this.isViewMode = false;
    } else {
      this.selectedInventory = null;
    }
    this.selectedStatus = null;
  }

  onHoldModalClose(isClose: boolean): void {
    this.showInventoryToBeHoldModal = false;
    this.getAll();
    if (isClose) {
      this.showCreateModal = true;
      this.selectedInventory = this.selectedInventory;
      this.activeIndex = Tabs.HOLD;
      this.showHoldTabs = true;
      this.isViewMode = false;
    } else {
      this.selectedInventory = null;
    }
    this.selectedStatus = null;
  }

  onDuplicateInventoryModalClose() {
    this.showDuplicateInventoryModal = false;
    this.selectedInventoryContext = null;
  }

  onViewEditClick(inventory: InventoryListItem) {
    this.showCreateModal = true;
    this.isViewMode = true;
    this.selectedInventory = inventory;
    this.activeIndex = Tabs.GENERAL;
    this.cdf.detectChanges();
    if (this.selectedInventory.generalInformation.unitStatus.id === 2) {
      this.showSoldTabs = true;
    }
    if (this.selectedInventory.generalInformation.unitStatus.id === 12) {
      this.showHoldTabs = true;
    }
  }

  onViewAssociation(unitId: number): void {
    const endpoint = API_URL_UTIL.inventory.inventoryDetails.replace(':unitId', unitId.toString())
    this.inventoryService.get<InventoryGeneralDetails>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (inventoryDetail) => {
        this.onViewEditClick(inventoryDetail as any)
      }
    });
  }

  onBackdropClick(element: HTMLElement): void {
    Utils.handlePopoverBackdropClick(element, this.selectedInventory, 'status', this.isActiveTab, this.cdf);
  }

  openSoldTab(inventory: InventoryListItem): void {
    this.isViewMode = false;
    this.showCreateModal = true;
    this.selectedInventory = inventory;
    this.activeIndex = Tabs.SALEINFORMATION;
    this.showSoldTabs = true;
  }
  
  openHoldTab(inventory: InventoryListItem): void {
    this.isViewMode = false;
    this.showCreateModal = true;
    this.selectedInventory = inventory;
    this.activeIndex = Tabs.HOLD;
    this.showHoldTabs = true;
  }

  private getCurrentUser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe({
        next: (user) => {
          if (user) {
            this.currentUser = user;
          }
          resolve();
        },
        error: () => {
          reject();
        }
      });
    });
  }

  toggleFilterSidebar() {
    this.showColumnModal = true;
  }

  toggleColumnSidebar() {
    this.showColumnModal = !this.showColumnModal;
  }

  sortColumnList(columns: ColumnItem[]) {
    const imageColumn: ColumnItem[] = columns.filter(x => x.name === 'Image');
    const tempData: ColumnItem[] = columns.filter(x => x.name === 'Stock' || x.name === 'Associated Stocks');
    const tempData1: ColumnItem[] = columns.filter(x => x.name !== 'Image' && x.name !== 'Stock' && x.name !== 'Action' && x.name !== 'Associated Stocks' && x.name !== 'Internet Groups');
    const tempData2: ColumnItem[] = columns.filter(x => x.name === 'Internet Groups');
    const tempData3: ColumnItem[] = columns.filter(x => x.name === 'Action');
    return [...imageColumn, ...tempData, ...tempData1, ...tempData2, ...tempData3];
  }

  getFilterSaveParams(): FilterList {
    return {
      module: FilterModuleName.INVENTORY_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.tableColumn?.id,
      hideField: ''
    }
  }
  getSpecificationFilterSaveParams(): FilterList {
    return {
      module: this.selectedTabName?.split(' ').join('_')?.toUpperCase() ?? '',
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      id: this.specificationColumn?.id,
      hideField: ''
    }
  }

  callFilterApiAgain(): void {
    this.getFilterDetail();
  }

  get filterInfoParams() {
    return {
      id: this.tableColumn?.id,
      module: FilterModuleName.INVENTORY_MODULE.toUpperCase(),
      skeyeUserId: this.currentUser?.id ? this.currentUser?.id : null,
      dealerId: this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.selectedColumns),
      hideField: null
    };
  }

  assignDataType(type: string): string {
    let stringDataType = '';
    switch (type) {
      case 'boolean':
        stringDataType = DataType.BOOLEAN
        break;
      case 'INTEGER':
        stringDataType = DataType.INTEGER
        break;
      case 'DROP_DOWN':
        stringDataType = DataType.STRING
        break;
      case 'MULTI_DROP_DOWN':
        stringDataType = DataType.LONG
        break;
      case 'DOUBLE':
        stringDataType = DataType.DOUBLE
        break;
      case 'DATE':
        stringDataType = DataType.DATE
        break;
      default:
        stringDataType = DataType.STRING
        break;
    }
    return stringDataType
  }

  assignOperator(type: string) {
    let operatorType = '';
    switch (type) {
      case 'INTEGER':
        operatorType = OperatorType.EQUAL
        break;
      case 'DATE':
        operatorType = OperatorType.GREATER_THAN_OR_EQUAL
        break;
      case 'DROP_DOWN':
        operatorType = OperatorType.EQUAL
        break;
      case 'DOUBLE':
        operatorType = OperatorType.EQUAL
        break;
      case 'MULTI_DROP_DOWN':
        operatorType = OperatorType.IN
        break;
      default:
        operatorType = OperatorType.LIKE
        break;
    }
    return operatorType
  }

  getEventValue(event: any, col: any) {
    let temp = '';
    switch (col.type) {
      case 'DATE':
        const endDate: any = this.datePipe.transform(event?.value ? event.value : event, dateFormat.format)
        temp = this.setStartDate(new Date(endDate).toISOString())
        break;
      default:
        temp = event.value
        break;
    }
    return temp
  }

  getFilterInfo(inputValue: any, col: any) {
    const existingValue = this.filterParams[col.searchKey as keyof InventoryListFilter];
    if (!existingValue && inputValue) {
      this.filterParams = { ...this.filterParams, [col.searchKey]: inputValue };
    } else {
      if (existingValue) {
        if (inputValue?.length) {
          this.filterParams = { ...this.filterParams, [col.searchKey]: inputValue };
        } else {
          delete this.filterParams[col.searchKey as keyof InventoryListFilter];
          this.filterParams = { ...this.filterParams, [col.searchKey]: inputValue };
        }
      }
      if (col.searchKey === 'isDisplayOnWeb' && !existingValue) {
        if (inputValue === null) {
          delete this.filterParams[col.searchKey as keyof InventoryListFilter];
        } else {
          this.filterParams = { ...this.filterParams, [col.searchKey]: inputValue };
        }
      }
    }
    if (col.type === 'DROP_DOWN' && (inputValue === 'All' || inputValue === '')) {
      const rm = this.filterParams?.status;
      if (rm) {
        delete this.filterParams?.status;
      }
    }
  }

  private setSpecificationArray(inputValue: string | number[] | null, col: ColumnItem): void {
    if (col?.name) {
      if (inputValue) {
        const specificationSearchObj = new InventorySpecificationSearchObj(col.name, inputValue, col.type ?? '');
        this.specificationSearchItems[col.name] = specificationSearchObj;
        if (Array.isArray(inputValue) && !inputValue.length) {
          delete this.specificationSearchItems[col.name];
        }
      } else {
        delete this.specificationSearchItems[col.name];
      }
    }
    this.filterParams.duplicateSpecification = this.transformSpecificationSearchObj();
  }


  private transformSpecificationSearchObj(): InventorySpecificationSearchDTO[][] {
    // There are 4 types of fields in specification fields, For Dropdown isString is FALSE (as in it we send ids i.e. numbers) and for all others its TRUE
    const specSearchFinalDto: InventorySpecificationSearchDTO[][] = [];
    Object.entries<InventorySpecificationSearchObj>(this.specificationSearchItems).forEach(([key, specObj]) => {
      const specSearchChildArray: InventorySpecificationSearchDTO[] = [];
      if (Array.isArray(specObj.value)) {
        specObj.value.forEach(val => {
          const specSearchChild = new InventorySpecificationSearchDTO();
          specSearchChild.searchKey = `"${specObj.label}":${val}`;
          specSearchChildArray.push(specSearchChild)
        })
      }
      else {
        const specSearchChild = new InventorySpecificationSearchDTO();
        // In case of isString = TRUE, for NUMBER field don't include " (1 double quote) in front of value, It's only needed for string values.
        specSearchChild.isString = true;
        specSearchChild.searchKey = `"${specObj.label}":"${specObj.value}`;
        if (specObj.type === 'INTEGER') {
          specSearchChild.searchKey = `"${specObj.label}":${specObj.value}`;
        }
        specSearchChildArray.push(specSearchChild)
      }
      specSearchFinalDto.push(specSearchChildArray)
    });
    return specSearchFinalDto;
  }

  tableSearchByColumn(event: any, col: ColumnItem) {
    this.isLoading = true;
    if (col.key === 'generalInformation.make.name') {
      this.makeIds = event.value ?? [];
    }
    if (col.key === 'generalInformation.unitModel.name') {
      this.modelIds = event.value ?? [];
    }
    if (col.key === 'generalInformation.unitType.name') {
      this.unitTypeIds = event.value ?? [];
    }
    this.inventories = [];
    const searchInput = this.getEventValue(event, col);
    if (col.isSpecificationField) {
      this.setSpecificationArray(searchInput, col);
    } else {
      this.getFilterInfo(searchInput, col);
      this.setSearchEndDate(event, col);
    }
    this.globalSearch.next(this.filterParams);
    const temp = this.selectedColumns.find(d => d.key === col.key);
    const temp1 = this.dropDownColumnList.find(d => d.key === col.searchKey);
    if (temp) {
      temp.value = (col.type === 'DATE') ? this.datePipe.transform(searchInput, 'MM/dd/yyyy') : searchInput;
      if (temp1) {
        temp1.value = temp.value;
      }
    }
  }

  setSearchEndDate(event: any, col: ColumnItem) {
    console.log(col);
    debugger;
    if (col.type === 'DATE') {
      const startDate: any = this.datePipe.transform(event?.value ? event.value : event, dateFormat.format);
      if (col.key === 'createdDate') {
        this.filterParams = { ...this.filterParams, endDate: this.setEndDate(new Date(startDate).toISOString()) };
      }
      if (col.key === 'financial.purchaseDate') {
        this.filterParams = { ...this.filterParams, purchaseEndDate: this.setEndDate(new Date(startDate).toISOString()) };
      }
    }

    // Handle range filters like date filters
    if (col.type === 'YEAR_RANGE') {
      // Clear existing year filters first
      delete this.filterParams.year;
      delete this.filterParams.startYear;
      delete this.filterParams.endYear;

      if (this.yearRange && this.yearRange.length >= 1) {
        const startYear = this.yearRange[0]?.getFullYear();
        const endYear = this.yearRange[1]?.getFullYear() || startYear;
        this.filterParams = { ...this.filterParams, startYear, endYear };
      }
    }

    if (col.type === 'ODOMETER_RANGE') {
      // Clear existing odometer filters first
      delete this.filterParams.odometerReading;
      delete this.filterParams.odometerReadingStart;
      delete this.filterParams.odometerReadingEnd;

      if (this.odometerRangeStart || this.odometerRangeEnd) {
        this.filterParams = {
          ...this.filterParams,
          odometerReadingStart: this.odometerRangeStart || undefined,
          odometerReadingEnd: this.odometerRangeEnd || undefined
        };
      }
    }

    if (col.type === 'PRICE_RANGE' && col.searchKey) {
      // Clear existing price filters first
      delete this.filterParams[col.searchKey as keyof InventoryListFilter];
      delete this.filterParams[`${col.searchKey}Start` as keyof InventoryListFilter];
      delete this.filterParams[`${col.searchKey}End` as keyof InventoryListFilter];

      const range = this.priceRanges[col.searchKey];
      if (range && (range.start || range.end)) {
        this.filterParams = {
          ...this.filterParams,
          [`${col.searchKey}Start`]: range.start || undefined,
          [`${col.searchKey}End`]: range.end || undefined
        };
      }
    }
  }

  displaySearchResult() {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.getAll();
      this.cdf.detectChanges();
    });
  }

  setEndDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 23, 59, 50).toISOString();
  }

  setStartDate(endDate: string): string {
    const date = new Date(endDate);
    return new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0).toISOString();
  }

  clearSearchInput() {
    this.selectedColumns.forEach(cols => {
      cols.value = null;
    });
    this.dropDownColumnList.forEach(cols => {
      cols.value = null;
    });
    this.specificationSearchItems = {};
    this.myPreference = this.appliedPreference = [];
    this.appliedFilterList = null;
    this.makeIds = this.modelIds = this.unitTypeIds = this.userIds = [];
    this.makes = this.models = this.unitTypes = [];
    this.createdDate = this.purchaseDate = null;
    // Clear range filters
    this.yearRange = null;
    this.odometerRangeStart = this.odometerRangeEnd = null;
    this.priceRanges = {
      retailAskingPrice: { start: null, end: null },
      initialInvestment: { start: null, end: null },
      actualInvestment: { start: null, end: null },
      totalProjectedInvestment: { start: null, end: null }
    };
    this.filterParams = { ...this.initialFilterParams };
    this.filterParams.orderBy = 'gi.stock_number';
    this.filterParams.direction = "asc";
    this.paginationConfig.predicate = this.filterParams?.orderBy;
    this.getAll();
  }

  exportUsersToExcel(downloadAll = false): void {
    this.isExporting = true;
    if (this.selectedInventories.length) {
      this.exportData(this.selectedInventories, downloadAll);
    } else {
      this.inventoryService.getListWithFiltersWithPagination<InventoryListFilter, InventoryListItem>
        (this.filterParams, 1, this.paginationConfig.totalElements, API_URL_UTIL.inventory.search)
        .pipe(takeUntil(this.destroy$)).subscribe(inventories => {
          this.exportData(inventories.content, downloadAll);
        });
    }
  }

  exportData(data: Array<InventoryListItem>, downloadAll: boolean) {
    import("xlsx").then(xlsx => {
      const inventory = this.getExcelData(data, downloadAll)
      const worksheet = xlsx.utils.json_to_sheet(inventory);
      const workbook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
      const excelBuffer: any = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
      Utils.saveAsExcelFile(excelBuffer, "inventories");
      this.isExporting = false;
      this.cdf.detectChanges();
    });
  }

  getExcelData(inventories: Array<InventoryListItem>, downloadAll = false) {
    const excelData: Array<ExcelData> = inventories.map(inventory => ({
      'Stock': inventory.generalInformation?.stockNumber,
      "Associated Stocks": inventory.associations?.map(assoc => assoc.stockNumber).join(', '),
      'Year': inventory.generalInformation?.year,
      'Make': inventory.generalInformation?.make?.name,
      'Model': inventory.generalInformation?.unitModel?.name,
      'Status': inventory.generalInformation?.unitStatus?.name,
      'Active': inventory.archived ? 'Archived' : 'Active',
      'Category': inventory.generalInformation?.unitTypeCategory?.name,
      'Unit Type': (inventory.generalInformation.unitType as UnitType)?.name,
      'Vin': inventory.generalInformation?.vin,
      "Designation": inventory.generalInformation?.designation?.name,
      "Owner": inventory.generalInformation?.owner?.name,
      "Created By": inventory.createdBy?.name,
      "Creation Date": Utils.dateIntoUserReadableFormat(inventory?.createdDate ?? ''),
      "Display On Web": inventory.internetOption?.displayOnWeb ? 'Yes' : 'No',
      "Internet Groups": inventory.internetGroups?.map(group => group.internetGroupName).join(', '),
      "Receiving Location": inventory.unitLotLocation?.receivingLocation?.name,
      "Current Location": inventory.unitLotLocation?.currentLocation?.name,
      "Retail Asking Price": Utils.formatCurrency(inventory?.financial?.retailAskingPrice),
      "Initial Investment": Utils.formatCurrency(inventory?.financial?.initialInvestment),
      "Purchase Date": Utils.dateIntoUserReadableFormat(inventory?.financial?.purchaseDate ?? ''),
      "Actual Investment": Utils.formatCurrency(inventory?.financial?.actualInvestment),
      "Total Projected Investment": Utils.formatCurrency(inventory?.financial?.totalProjectedInvestment),
      "Sales Person": inventory?.salesPerson?.name,
      "Odometer": inventory?.odometerReading ? new Intl.NumberFormat('en-US').format(Number(inventory?.odometerReading)) : null,
    }))
    if (!downloadAll) {
      const selectedKeysToBeDisplayedInExcel = this.userSelectedDefaultColumnsArray.map(({ name }) => name);
      for (const [index, data] of excelData.entries()) {
        for (const key in data) {
          if (!selectedKeysToBeDisplayedInExcel.includes(key)) {
            delete (excelData as any)[index][key]
          }
        }
      }
    }
    return excelData;
  }

  showCloneModal(item: InventoryListItem): void {
    this.selectedInventoryContext = item;
    this.showDuplicateInventoryModal = true;
  }

  getCategoryTypes() {
    const endpoint = `${API_URL_UTIL.admin.categorySpecification.categoryCount}?isInventory=true&archived=${this.isArchived()}`;
    this.categoryService.getList<Category>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: Array<Category>) => {
        this.categoryTypes = categoryTypes;
        this.getFilterDetail();
        this.cdf.detectChanges();
      }
    });
  }


  getInternetGroupList(): void {
      this.commonService.getList(API_URL_UTIL.internetGroups.root).pipe(takeUntil(this.destroy$)).subscribe({
        next: (internetGroups) => {
          this.internetGroupOptions = internetGroups;
          this.cdf.detectChanges();
        }
      });
  }
  
  isArchived(): boolean {
    return this.activeTabIndex === 1;
  }

  clearMakes(col: ColumnItem): void {
    this.makeIds = [];
    this.tableSearchByColumn([], col);
  }

  clearModels(col: ColumnItem): void {
    this.modelIds = [];
    this.tableSearchByColumn([], col);
  }

  clearUnitTypes(col: ColumnItem): void {
    this.unitTypeIds = [];
    this.tableSearchByColumn([], col);
  }

  clearUsers(col: ColumnItem): void {
    this.userIds = [];
    this.tableSearchByColumn([], col);
  }

  showDetails(data: InventoryListItem) {
    this.selectedInventory = data;
    this.showInventoryDetails = true;
  }

  getInventoriesCount() {
    return this.inventories.length;
  }

  getSelectedInventoryCount() {
    return this.selectedInventories.length;
  }

  isInventorySelected(inventory: InventoryListItem): boolean {
    return this.selectedInventories.some(selected => selected.id === inventory.id);
  }

  toggleHistorySidebar() {
    this.showHistoryModal = !this.showHistoryModal;
  }

  togglePreferenceModal() {
    this.myPreference = [];
    this.showPreferenceModal = false;
  }

  togglePreferenceListModal() {
    this.showPreferenceListModal = !this.showPreferenceListModal;
  }

  getFilterDetail(): void {
    this.userSelectedColumnsArray = this.dropDownColumnList = this.userSelectedDefaultColumnsArray = this.userSelectedSpecificationColumnsArray = [];
    const userId = this.currentUser?.id ?? null;
    if (userId) {
      this.getSelectedDefaultColumns(userId)
    }
  }

  clearDate(col: ColumnItem) {
    if (col.key === 'createdDate') {
      this.filterParams.startDate = undefined
      this.filterParams.endDate = undefined
    } else if (col.key === 'financial.purchaseDate') {
      this.filterParams.purchaseStartDate = undefined
      this.filterParams.purchaseEndDate = undefined
    }
    this.getAll()
  }

  private getSelectedDefaultColumns(userId: number): void {
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${FilterModuleName.INVENTORY_MODULE}`);
    this.columnDropDownService.getList<FilterList>(endpoint).pipe(
      takeUntil(this.destroy$),
    ).subscribe((filters: FilterList[]) => {
      this.defaultTabs = filters.filter((item: FilterList) => item.filterType === "TAB" && item.filterName !== 'Recently Added');
      if (!this.selectedTabName) {
        this.selectedTabName = this.defaultTabs[0].filterName;
      }
      this.recentlyAddedFilter = filters.find((item: FilterList) => item.filterType === "TAB" && item.filterName === 'Recently Added');
      this.tableColumn = filters.find((item: FilterList) => item.filterType === "COLUMN");
      if (this.tableColumn?.data) {
        this.userSelectedDefaultColumnsArray = JSON.parse(this.tableColumn.data);
      }
      this.checkIfActiveTabIsCategoryTab();
      this.cdf.detectChanges();
    });
  }

  private checkIfActiveTabIsCategoryTab(isApplyPreference = false): void {
    const userId = this.currentUser?.id ?? null;
    this.isCategoryTab = this.categoryTypes.some(category => category.name === this.selectedTabName);

    if (this.isCategoryTab && userId) {
      forkJoin([
        this.getSelectedSpecificationColumns(userId)
      ]).subscribe(([specificationColumns]) => {
        this.userSelectedSpecificationColumnsArray = specificationColumns;
        this.userSelectedColumnsArray = [...this.userSelectedDefaultColumnsArray, ...this.userSelectedSpecificationColumnsArray];
      });
    } else {
      this.userSelectedSpecificationColumnsArray = this.specificationDropDownColumnList = [];
      this.userSelectedColumnsArray = [...this.userSelectedDefaultColumnsArray, ...this.userSelectedSpecificationColumnsArray];
    }
    if (!this.userSelectedDefaultColumnsArray.length) {
      this.getDropDownColumnList(true);
    } else {
      this.getDropDownColumnList();
    }

    const isInitializing = !this.inventories.length && !isApplyPreference;
    if (!isApplyPreference && !isInitializing) {
      this.getAll();
    }
    this.cdf.detectChanges();
  }

  getInventorySpecificationsForm(categoryId?: number): void {
    if (categoryId || this.selectedCategoryId) {
      this.inventorySpecificationService.get<InventorySpecificationResponse>(
        categoryId ?? this.selectedCategoryId!,
        `${API_URL_UTIL.specificationMasters.latest}`).pipe(takeUntil(this.destroy$)).subscribe(
          {
            next: (res: InventorySpecificationResponse) => {
              this.categorySpecification = res.masterData.specification;
              this.cdf.detectChanges();
            }
          }
        )
    }
  }

  private getSelectedSpecificationColumns(userId: number): Observable<ColumnItem[]> {
    const moduleName = this.defaultTabs[this.activeTabIndex]?.filterName?.split(' ').join('_')?.toUpperCase();
    const endpoint = `${API_URL_UTIL.filter.filterDataByUserIdAndModule}`.replace(':userId', String(userId)).concat(`?module=${moduleName}`);
    return this.columnDropDownService.getList<FilterList>(endpoint).pipe(
      takeUntil(this.destroy$),
      map((filters) => {
        this.specificationColumn = filters.find((item: FilterList) => item.filterType === 'COLUMN');
        if (this.specificationColumn?.data) {
          this.userSelectedSpecificationColumnsArray = JSON.parse(this.specificationColumn.data);
          this.cdf.detectChanges();
          return this.userSelectedSpecificationColumnsArray;
        } else {
          return [];
        }
      })
    );
  }

  private createDefaultColumns(): void {
    const defaultFields = this.defaultDropDownColumnList.filter(a => a.default ? a.name : null);
    if (this.tableColumn === undefined && defaultFields?.length) {
      this._selectedColumns = defaultFields;
      this.columnDropDownService.add<FilterList>(this.filterInfoParams, '').pipe(takeUntil(this.destroy$)).subscribe({
        next: (res) => {
          if (res.filterType === 'COLUMN') {
            this.tableColumn = res;
            this.userSelectedDefaultColumnsArray = JSON.parse(res.data);
            this.userSelectedColumnsArray = [...this.userSelectedDefaultColumnsArray, ...this.userSelectedSpecificationColumnsArray];
            this.sortSelectedColumns();
            this.cdf.detectChanges();
          }
        }
      });
    }
  }

  private getDropDownColumnList(shouldCreateDefaultColumn = false) {
    forkJoin([
      this.getDefaultColumnMaster(),
      this.getSpecificationColumnMaster()
    ]).subscribe(
      ([defaultColumns, specificationColumns]) => {
        this.dropDownColumnList = [...this.defaultDropDownColumnList, ...this.specificationDropDownColumnList];
        if (shouldCreateDefaultColumn) {
          this.createDefaultColumns();
        } else {
          this.handleColumnUpdates();
        }
        this.cdf.detectChanges();
      }
    );
  }

  private getDefaultColumnMaster(): Observable<ColumnItem[]> {
    const endpoint = `${API_URL_UTIL.columnMasters.moduleWithoutSlash}`.concat(`?module=${FilterModuleName.INVENTORY_MODULE.toUpperCase()}`);
    return this.columnMasterService.get<SpecificationMasterColumn[]>(endpoint).pipe(
      takeUntil(this.destroy$),
      map((response) => {
        if (response?.length) {
          this.defaultDropDownColumnList = response[0]?.masterData?.columns;
          this.cdf.detectChanges();
          return this.defaultDropDownColumnList;
        }
        return [];
      })
    );
  }

  private getSpecificationColumnMaster(): Observable<ColumnItem[]> {
    if (this.isCategoryTab) {
      const moduleName = this.defaultTabs[this.activeTabIndex]?.filterName?.split(' ').join('_')?.toUpperCase();
      const endpoint = `${API_URL_UTIL.columnMasters.moduleWithoutSlash}`.concat(`?module=${moduleName}`);
      return this.columnMasterService.get<SpecificationMasterColumn[]>(endpoint).pipe(
        takeUntil(this.destroy$),
        map((res) => {
          if (res?.length) {
            this.specificationDropDownColumnList = res[0]?.masterData?.columns;
            this.cdf.detectChanges();
            return this.specificationDropDownColumnList;
          } else {
            return [];
          }
        })
      );
    } else {
      this.specificationDropDownColumnList = [];
      return of([]);
    }
  }

  private sortSelectedColumns() {
    let temp: ColumnItem[] = [];
    if (this.appliedPreference.length) {
      this.setPreferenceValues();
    }
    const keys = this.userSelectedColumnsArray.map(a => a.key);
    this.userSelectedColumnsArray = this.sortColumnList(this.userSelectedColumnsArray)
    this._selectedColumns = this.cols = this.userSelectedColumnsArray;

    if (this.userSelectedColumnsArray.length) {
      temp = [...this.userSelectedColumnsArray];
    }

    this.dropDownColumnList.forEach((column: ColumnItem) => {
      if (!keys.includes(column.key)) {
        temp.push(column);
      }
    });
    this.dropDownColumnList = temp;
    this.dropDownColumnList = this.sortColumnList(this.dropDownColumnList);
    if (this.appliedPreference.length) {
      this.getAllWithPreferenceValue()
    }
    this.cdf.detectChanges();
  }

  private handleColumnUpdates(): void {
    const filterDataColumns = [...this.userSelectedDefaultColumnsArray, ...this.userSelectedSpecificationColumnsArray];
    const masterDataColumns = [...this.defaultDropDownColumnList, ...this.specificationDropDownColumnList];

    if (filterDataColumns.length === 0 && masterDataColumns.length > 0) {
      this.createDefaultColumns();
      return;
    }
    this.syncFilterWithMaster();
  }

  private syncFilterWithMaster(): void {
    const newDefaultColumns = this.syncColumns(
      this.userSelectedDefaultColumnsArray,
      this.defaultDropDownColumnList
    );

    const newSpecificationColumns = this.syncColumns(
      this.userSelectedSpecificationColumnsArray,
      this.specificationDropDownColumnList
    );

    const defaultChanged = this.hasChanges(this.userSelectedDefaultColumnsArray, newDefaultColumns);
    const specificationChanged = this.hasChanges(this.userSelectedSpecificationColumnsArray, newSpecificationColumns);

    if (defaultChanged || specificationChanged) {
      this.updateFilterData(newDefaultColumns, newSpecificationColumns);
    } else {
      this.sortSelectedColumns();
    }
  }

  private syncColumns(filterColumns: ColumnItem[], masterColumns: ColumnItem[]): ColumnItem[] {
    const result: ColumnItem[] = [];

    filterColumns.forEach(filterCol => {
      const masterCol = masterColumns.find(m => m.key === filterCol.key);
      if (masterCol) {
        result.push({ ...masterCol });
      }
    });

    masterColumns.forEach(masterCol => {
      const existsInResult = result.some(r => r.key === masterCol.key);
      if (!existsInResult && masterCol.default) {
        result.push({ ...masterCol });
      }
    });

    return result;
  }

  private hasChanges(oldArray: ColumnItem[], newArray: ColumnItem[]): boolean {
    if (oldArray.length !== newArray.length) {
      return true;
    }

    for (let i = 0; i < oldArray.length; i++) {
      if (JSON.stringify(oldArray[i]) !== JSON.stringify(newArray[i])) {
        return true;
      }
    }

    return false;
  }

  private updateFilterData(newDefaultColumns: ColumnItem[], newSpecificationColumns: ColumnItem[]): void {
    if (this.hasChanges(this.userSelectedDefaultColumnsArray, newDefaultColumns) && this.tableColumn?.id) {
      this.updateDefaultColumns(newDefaultColumns);
    }

    if (this.hasChanges(this.userSelectedSpecificationColumnsArray, newSpecificationColumns) && this.specificationColumn?.id) {
      this.updateSpecificationColumns(newSpecificationColumns);
    }

    this.userSelectedDefaultColumnsArray = newDefaultColumns;
    this.userSelectedSpecificationColumnsArray = newSpecificationColumns;
    this.userSelectedColumnsArray = [...newDefaultColumns, ...newSpecificationColumns];
    this.sortSelectedColumns();
  }

  private updateDefaultColumns(newColumns: ColumnItem[]): void {
    const params = {
      ...this.filterInfoParams,
      data: JSON.stringify(newColumns)
    };
    this.columnDropDownService.update<FilterList>(params).pipe(takeUntil(this.destroy$)).subscribe();
  }

  private updateSpecificationColumns(newColumns: ColumnItem[]): void {
    const params = {
      ...this.filterInfoParams,
      data: JSON.stringify(newColumns)
    };
    this.columnDropDownService.update<FilterList>(params).pipe(takeUntil(this.destroy$)).subscribe();
  }

  private getAllWithPreferenceValue(): void {
    const colWithValues: ColumnItem[] = this.userSelectedColumnsArray.filter((col: ColumnItem) => {
      return col.value
    })
    colWithValues.forEach((col: ColumnItem) => {
      this.tableSearchByColumn({ value: col?.value }, col);
    })
  }

  private setPreferenceValues(): void {
    this.appliedPreference.forEach(preference => {
      const columnItem = this.userSelectedColumnsArray.find(col => col.key === preference.key);
      if (columnItem) {
        columnItem.value = preference.value;
      } else {
        this.userSelectedColumnsArray.push(preference);
      }
    })
  }

  getAllOptions(col: ColumnItem): IdNameModel[] {
    if (typeof col?.parentIndex === 'number' && typeof col?.childIndex === 'number') {
      const fieldData = this.categorySpecification[col.parentIndex].fields[col.childIndex];
      return fieldData?.options;
    }
    return [];
  }

  getFilterDataCountParams(): any {
    const data: any = {};

    const columnsFromApi = this.defaultDropDownColumnList;
    const filterParams = this.filterParams;

    columnsFromApi.forEach(column => {
      const searchKey = column.searchKey;
      const type = column.type;

      if (!searchKey) return;

      if (type === 'DATE') {
        if (searchKey === 'startDate' && filterParams.startDate) {
          data.startDate = filterParams.startDate;
          data.endDate = filterParams.endDate;
        } else if (searchKey === 'purchaseStartDate' && filterParams.purchaseStartDate) {
          data.purchaseStartDate = filterParams.purchaseStartDate;
          data.purchaseEndDate = filterParams.purchaseEndDate;
        }
      } else if (type === 'DOUBLE') {
        if (searchKey === 'retailAskingPrice' && (filterParams.retailAskingPriceStart || filterParams.retailAskingPriceEnd)) {
          data.retailAskingPriceStart = filterParams.retailAskingPriceStart;
          data.retailAskingPriceEnd = filterParams.retailAskingPriceEnd;
        }
        else if (searchKey === 'initialInvestment' && (filterParams.initialInvestmentStart || filterParams.initialInvestmentEnd)) {
          data.initialInvestmentStart = filterParams.initialInvestmentStart;
          data.initialInvestmentEnd = filterParams.initialInvestmentEnd;
        }
        else if (searchKey === 'actualInvestment' && (filterParams.actualInvestmentStart || filterParams.actualInvestmentEnd)) {
          data.actualInvestmentStart = filterParams.actualInvestmentStart;
          data.actualInvestmentEnd = filterParams.actualInvestmentEnd;
        }
        else if (searchKey === 'totalProjectedInvestment' && (filterParams.totalProjectedInvestmentStart || filterParams.totalProjectedInvestmentEnd)) {
          data.totalProjectedInvestmentStart = filterParams.totalProjectedInvestmentStart;
          data.totalProjectedInvestmentEnd = filterParams.totalProjectedInvestmentEnd;
        }
      } else if (searchKey === 'odometerReading' && (filterParams.odometerReadingStart || filterParams.odometerReadingEnd)) {
        data.odometerReadingStart = filterParams.odometerReadingStart;
        data.odometerReadingEnd = filterParams.odometerReadingEnd;
      }
      else if (searchKey === 'year' && (filterParams.startYear || filterParams.endYear)) {
        data.startYear = filterParams.startYear;
        data.endYear = filterParams.endYear;
      }
      else {
        const value = filterParams[searchKey];
        if (value !== undefined && value !== null && value !== '') {
          data[searchKey] = value;
        }
      }
    });

    return data;
  }


  getFilterDataWithCount(): void {
    this.commonService.getInventoryFilterDataWithCount(this.getFilterDataCountParams(), API_URL_UTIL.filter.counts).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: FilterDataWithCounts) => {
        this.filterDataWithCounts = res;
        this.categoryTypesOptions = this.filterDataWithCounts?.categories;
        this.makes = this.filterDataWithCounts?.makes;
        this.models = this.filterDataWithCounts?.models;
        this.unitTypes = this.filterDataWithCounts?.unitTypes;
        this.currentLocations = this.filterDataWithCounts?.currentLocation;
        this.receivingLocations = this.filterDataWithCounts?.receivingLocation;
        this.displayOnWebOptions = this.filterDataWithCounts?.displayOnWeb.map(option => ({
          ...option,
          value: option.name === 'Yes' ? true : false
        }));
        this.inventoryStatusesWithCount = this.filterDataWithCounts?.statuses;
        this.createdByUsers = this.filterDataWithCounts?.createdBy;
        this.salesPersonOptions = this.filterDataWithCounts?.salesPersons;
        this.cdf.detectChanges();
      }
    });
  }

  onInventorySortChange(sortEvent: any): void {
    this.paginationConfig.predicate = sortEvent.field as string;
    this.paginationConfig.ascending = sortEvent.order === 1;
    this.filterParams.orderBy = sortEvent.field as string;
    this.filterParams.direction = sortEvent.order === 1 ? 'asc' : 'desc';
    this.getAll();
  }

  saveMyPreference(): void {
    this.myPreference = this.selectedColumns.filter(col => col.value);
    if (!this.myPreference.length) {
      this.toasterService.error('Please filter at least one column to save preference');
      return;
    } else {
      this.showPreferenceModal = true;
    }
  }

  updateMyPreference(): void {
    this.myPreference = this.selectedColumns.filter(col => col.value);
    if (!this.myPreference.length) {
      this.toasterService.error('Please filter at least one column to update preference');
      return;
    } else {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        message: MESSAGES.updatePreferenceWarning.replace('{record}', `"${this.appliedFilterList?.filterName}"`),
        icon: icons.triangle,
        header: "Confirmation",
        accept: () => {
          if (this.appliedFilterList) {
            this.appliedFilterList.data = JSON.stringify(this.myPreference);
            this.columnDropDownService.update(this.appliedFilterList).pipe(takeUntil(this.destroy$)).subscribe({
              next: () => {
                this.toasterService.success(MESSAGES.preferenceMessage.replace("{action}", "updated"));
              }
            });
          }
        }
      });
    }
  }

  trackByColumnFn(index: number, column: any): string {
    return column.key;
  }

  trackByGroupFn(index: number, group: any): number {
    return group.id || index;
  }

  applyPreference(filter: FilterList): void {
    this.appliedFilterList = filter;
    this.showPreferenceListModal = false;
    const indexOfTab = this.defaultTabs.findIndex(tab => tab.filterName === filter.activeTabName);
    this.onTabChanged({ index: indexOfTab }, JSON.parse(filter?.data ?? '') as ColumnItem[], true);
  }



  deletePreference(filter: FilterList): void {
    if (this.appliedFilterList && (this.appliedFilterList.id === filter.id)) {
      this.clearSearchInput();
    }
  }

  onCloseModal(): void {
    this.showInventoryDetailsModal = false;
    this.selectedQuotation = {} as QuotationResponse;
  }

  onSaveQuotation() {
    this.showInventoryDetailsModal = false;
    this.selectedQuotation = {} as QuotationResponse;
    this.toasterService.success(MESSAGES.quotationSendSuccess);
  }

  getInventoryMatchedParam(inventory: InventoryListItem): InventoryMatchedParams {
    return {
      unitTypeId: inventory.generalInformation?.unitType?.id,
      makeIds: [Number(inventory.generalInformation?.make.id)],
      unitModelIds: [inventory.generalInformation?.unitModel.id],
      categoryId: inventory.generalInformation?.unitTypeCategory?.id
    }
  }

  shareDetails(inventory: InventoryListItem): void {
    this.commonSharedService.setBlockUI$(true);
    this.commonService.addWithoutBaseUrl(this.getInventoryMatchedParam(inventory), API_URL_UTIL.admin.crm.customerInventoryMatched).pipe(takeUntil(this.destroy$)).subscribe({
      next: (quotations) => {
        const specificQuotation = quotations.find((quotation: any) => quotation.id === inventory.id);
        if (specificQuotation) {
          this.selectedQuotation = specificQuotation;
        }
        this.commonSharedService.setBlockUI$(false);
        this.showInventoryDetailsModal = true;
        this.cdf.detectChanges();
      }
    });
  }

  downloadPDF(inventory: InventoryListItem): void {
    this.commonSharedService.setBlockUI$(true);
    this.inventoryService.downloadPdf(this.transformData(inventory)).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        saveAs(new Blob([res], { type: "application/pdf" }), `Inventory_Details_${inventory?.generalInformation?.stockNumber}`);
        this.commonSharedService.setBlockUI$(false);
      }
    });
  }

  private transformData(inventory: InventoryListItem): QuotationUnitDTOS {
    const InventoryUnitDTOS: QuotationUnitDTOS = { unitId: inventory?.unitSpecification?.unitId ?? inventory.generalInformation.id, specifications: [] };
    inventory?.unitSpecification?.specificationData.specification.forEach((specification: InventoryListingSpecificationData) => {
      const transformedSpecification: Specifications = {
        groupName: specification.sectionName,
        fields: [],
      };
      if (specification.fields.some(field => field.value)) {
        specification.fields.forEach((field: SpecificationField) => {
          if (field.value) {
            if (field.dataType === "DropDown") {
              const dropdownOption = field.options.find((option: IdNameModel) => option.id === field.value);
              transformedSpecification.fields.push({
                key: field.label,
                value: dropdownOption?.name ?? field.value,
              });
            }
            else {
              transformedSpecification.fields.push({
                key: field.label,
                value: field.value,
              });
            }
          }
        });
        InventoryUnitDTOS.specifications.push(transformedSpecification);
      }
    });
    InventoryUnitDTOS.specifications.sort((a, b) => a.groupName.localeCompare(b.groupName));
    return InventoryUnitDTOS;
  }

  updateDisplayOnWeb(displayOnWeb: boolean, unitId: number): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: displayOnWeb ? MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'want to') : MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'dont want to'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDisplayOnWebConfirmation(displayOnWeb, unitId);
      },
      reject: () => {
        this.getAll();
        this.cdf.detectChanges();
      }
    });
  }

  private onDisplayOnWebConfirmation(displayOnWeb: boolean, unitId: number): void {
    this.commonSharedService.setBlockUI$(true);
    const endpoint = `${API_URL_UTIL.inventory.internetOptions}`;
    const requestParams = [{
      id: Number(unitId),
      value: displayOnWeb
    }]
    this.generalInfoService.updateDisplayOnWeb(requestParams, endpoint).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          const message = displayOnWeb ? MESSAGES.updateDisplayOnWebSuccess.replace('{action}', 'will be') : MESSAGES.updateDisplayOnWebSuccess.replace('{action}', 'will not be')
          this.toasterService.success(message);
          this.getAll();
          this.cdf.detectChanges();
        }
      });
  }

  // Range filter methods

  // Year Range Filter Methods
  onYearRangeSelect(event: any): void {
    // Create a dummy column for year range filter
    const yearCol: ColumnItem = {
      id: null,
      name: 'Year Range',
      key: 'yearRange',
      disable: false,
      default: false,
      shorting: false,
      type: 'YEAR_RANGE',
      value: null,
      shortingKey: null,
      searchKey: 'year'
    };
    this.tableSearchByColumn({ value: this.yearRange }, yearCol);
  }

  clearYearRange(): void {
    this.yearRange = null;
    const yearCol: ColumnItem = {
      id: null,
      name: 'Year Range',
      key: 'yearRange',
      disable: false,
      default: false,
      shorting: false,
      type: 'YEAR_RANGE',
      value: null,
      shortingKey: null,
      searchKey: 'year'
    };
    this.tableSearchByColumn({ value: null }, yearCol);
  }

  // Odometer Range Filter Methods
  openOdometerRangeModal(): void {
    this.tempOdometerRangeStart = this.odometerRangeStart;
    this.tempOdometerRangeEnd = this.odometerRangeEnd;
    this.showOdometerRangeModal = true;
  }

  applyOdometerRangeModal(): void {
    this.odometerRangeStart = this.tempOdometerRangeStart;
    this.odometerRangeEnd = this.tempOdometerRangeEnd;

    // Create a dummy column for odometer range filter
    const odometerCol: ColumnItem = {
      id: null,
      name: 'Odometer Range',
      key: 'odometerRange',
      disable: false,
      default: false,
      shorting: false,
      type: 'ODOMETER_RANGE',
      value: null,
      shortingKey: null,
      searchKey: 'odometerReading'
    };

    this.showOdometerRangeModal = false;
    this.tableSearchByColumn({ value: [this.odometerRangeStart, this.odometerRangeEnd] }, odometerCol);
  }

  cancelOdometerRangeModal(): void {
    this.tempOdometerRangeStart = null;
    this.tempOdometerRangeEnd = null;
    this.showOdometerRangeModal = false;
  }

  clearOdometerRangeModal(): void {
    this.tempOdometerRangeStart = null;
    this.tempOdometerRangeEnd = null;
    this.odometerRangeStart = null;
    this.odometerRangeEnd = null;

    const odometerCol: ColumnItem = {
      id: null,
      name: 'Odometer Range',
      key: 'odometerRange',
      disable: false,
      default: false,
      shorting: false,
      type: 'ODOMETER_RANGE',
      value: null,
      shortingKey: null,
      searchKey: 'odometerReading'
    };

    this.showOdometerRangeModal = false;
    this.tableSearchByColumn({ value: null }, odometerCol);
  }

  getOdometerRangeDisplayText(): string {
    if (this.odometerRangeStart && this.odometerRangeEnd) {
      return `${this.odometerRangeStart.toLocaleString()} - ${this.odometerRangeEnd.toLocaleString()} miles`;
    } else if (this.odometerRangeStart) {
      return `From ${this.odometerRangeStart.toLocaleString()} miles`;
    } else if (this.odometerRangeEnd) {
      return `Up to ${this.odometerRangeEnd.toLocaleString()} miles`;
    }
    return '';
  }

  // Price Range Filter Methods
  openPriceRangeModal(priceField: string): void {
    this.currentPriceField = priceField;
    const currentRange = this.priceRanges[priceField];
    this.tempPriceRangeStart = currentRange.start;
    this.tempPriceRangeEnd = currentRange.end;
    this.showPriceRangeModal = true;
  }

  applyPriceRangeModal(): void {
    if (this.currentPriceField) {
      this.priceRanges[this.currentPriceField] = {
        start: this.tempPriceRangeStart,
        end: this.tempPriceRangeEnd
      };

      // Create a dummy column for price range filter
      const priceCol: ColumnItem = {
        id: null,
        name: `${this.currentPriceField} Range`,
        key: `${this.currentPriceField}Range`,
        disable: false,
        default: false,
        shorting: false,
        type: 'PRICE_RANGE',
        value: null,
        shortingKey: null,
        searchKey: this.currentPriceField
      };

      this.showPriceRangeModal = false;
      this.tableSearchByColumn({ value: [this.tempPriceRangeStart, this.tempPriceRangeEnd] }, priceCol);
    } else {
      this.showPriceRangeModal = false;
    }
  }

  cancelPriceRangeModal(): void {
    this.tempPriceRangeStart = null;
    this.tempPriceRangeEnd = null;
    this.showPriceRangeModal = false;
  }

  clearPriceRangeModal(): void {
    if (this.currentPriceField) {
      this.tempPriceRangeStart = null;
      this.tempPriceRangeEnd = null;
      this.priceRanges[this.currentPriceField] = { start: null, end: null };

      const priceCol: ColumnItem = {
        id: null,
        name: `${this.currentPriceField} Range`,
        key: `${this.currentPriceField}Range`,
        disable: false,
        default: false,
        shorting: false,
        type: 'PRICE_RANGE',
        value: null,
        shortingKey: null,
        searchKey: this.currentPriceField
      };

      this.showPriceRangeModal = false;
      this.tableSearchByColumn({ value: null }, priceCol);
    } else {
      this.showPriceRangeModal = false;
    }
  }

  getPriceRangeDisplayText(priceField: string): string {
    const range = this.priceRanges[priceField];
    if (range.start && range.end) {
      return `$${range.start.toLocaleString()} - $${range.end.toLocaleString()}`;
    } else if (range.start) {
      return `From $${range.start.toLocaleString()}`;
    } else if (range.end) {
      return `Up to $${range.end.toLocaleString()}`;
    }
    return '';
  }

  getPriceModalHeader(): string {
    const fieldNames: { [key: string]: string } = {
      retailAskingPrice: 'Retail Asking Price',
      initialInvestment: 'Initial Investment',
      actualInvestment: 'Actual Investment',
      totalProjectedInvestment: 'Total Projected Investment'
    };
    return `Select ${fieldNames[this.currentPriceField] || 'Price'} Range`;
  }
}
