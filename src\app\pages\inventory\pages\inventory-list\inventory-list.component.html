<app-page-header [pageTitle]="pageTitle">
  <div headerResetBtn>
    <button pTooltip="Reset filters" type="button" class="btn btn-primary btn-sm inventory-reset-btn ms-2" (click)="clearSearchInput()">
      <fa-icon [icon]="faIcons.faArrowRotateRight"></fa-icon>
    </button>
  </div>
  <div headerActionBtn>
    <button
      type="button"
      class="btn btn-primary recent-btn me-sm-3 me-2"
      (click)="recentlyAddedData(recentlyAddedFilter)"
      *ngIf="recentlyAddedFilter && activeTabIndex !== defaultTabs.length && hasRecentData"
    >
      <span class="text-span"> {{ recentlyAddedFilter.filterName }}</span>
      <span class="img-span"><img [src]="constants.staticImages.icons.recentlyAdded" alt="recentlyAdded-icon" /></span>
    </button>
    <button class="btn btn-primary me-sm-3 me-2 show-label" type="button" (click)="showPreferenceListModal = true">My Preferences</button>
    <button class="btn btn-primary me-3 show-label big-label-btn" type="button" *appHasPermission="[permissionActions.VIEW_ACTIVITY]" (click)="toggleHistorySidebar()">
      <fa-icon [icon]="faIcons.faClockRotateLeft" class="btn-icon"></fa-icon>
      <span class="btn-label">Activities</span>
    </button>
    <button
      class="btn btn-primary left export me-sm-3 me-2 show-label"
      type="button"
      [appImageIconSrc]="constants.staticImages.icons.exportFile"
      (click)="excelDownloadMenu.toggle($event)"
      [disabled]="!paginationConfig.totalElements"
    >
      <span class="show-label"
        >Export<span *ngIf="getSelectedInventoryCount()">({{ getSelectedInventoryCount() }})</span
        ><fa-icon [icon]="faIcons.faSpinner" [spin]="true" class="ms-2" *ngIf="isExporting"></fa-icon
      ></span>
    </button>
    <p-menu #excelDownloadMenu [model]="excelDownloadMenuItems" [popup]="true"> </p-menu>
    <button *ngIf="getSelectedInventoryCount()" (click)="archiveSelected($event)" class="btn btn-primary me-sm-3 me-2">
      <span>{{ activeTabIndex === 1 ? 'Unarchive' : 'Archive' }}&nbsp;({{ getSelectedInventoryCount() }})</span>
    </button>
    <button (click)="deleteSelected($event)" *ngIf="getSelectedInventoryCount() && activeTabIndex === 1" class="btn btn-primary me-sm-3 me-2">
      <span>Delete &nbsp;({{ getSelectedInventoryCount() }})</span>
    </button>
    <button class="btn btn-primary left" [appImageIconSrc]="constants.staticImages.icons.addNew" (click)="onAdd()" *appHasPermission="[permissionActions.CREATE_INVENTORY]">
      <span class="show-label">Add New Unit</span>
    </button>
    <button *ngIf="!getSelectedInventoryCount()" class="btn btn-primary left column-btn" (click)="toggleFilterSidebar()">
      <span>Columns</span>
      <fa-icon [icon]="faIcons.faCaretDown"></fa-icon>
    </button>
  </div>
</app-page-header>
<div class="card">
  <div class="tabs">
    <ng-container *ngIf="defaultTabs">
      <p-tabView (onChange)="onTabChanged($event)" [(activeIndex)]="activeTabIndex" [scrollable]="true" styleClass="dynamic-tabs">
        <ng-container *ngFor="let tab of defaultTabs">
          <p-tabPanel *ngIf="tab.filterName" [header]="tab.filterName">
            <ng-template pTemplate="content">
              <ng-container [ngTemplateOutlet]="activeTabTemplate" [ngTemplateOutletContext]="{ heading: tab.filterName }"></ng-container>
            </ng-template>
          </p-tabPanel>
        </ng-container>
        <p-tabPanel *ngIf="recentlyAddedFilter && recentlyAdded" [header]="recentlyAddedFilter.filterName ?? ''">
          <ng-template pTemplate="content">
            <ng-container
              [ngTemplateOutlet]="activeTabTemplate"
              [ngTemplateOutletContext]="{
                heading: recentlyAddedFilter.filterName ?? ''
              }"
            ></ng-container>
          </ng-template>
        </p-tabPanel>
      </p-tabView>
    </ng-container>
  </div>
  <ng-template #activeTabTemplate>
    <p-table
      [columns]="selectedColumns"
      [value]="inventories"
      [(selection)]="selectedInventories"
      responsiveLayout="scroll"
      styleClass="p-datatable-gridlines"
      [lazy]="true"
      class="inventory-page-table"
      [reorderableColumns]="true"
      (onSort)="onInventorySortChange($event)"
      [customSort]="true"
      [rowHover]="true"
      [loading]="isLoading"
      [showLoader]="false"
      [resizableColumns]="true"
      columnResizeMode="expand"
      sortMode="single"
      [sortField]="paginationConfig.predicate"
      [scrollable]="true"
    >
      <ng-template pTemplate="header" let-columns>
        <!-- heading row -->
        <tr class="inventory-heading-tr">
          <ng-container *appHasPermission="[permissionActions.DELETE_INVENTORY || permissionActions.UPDATE_INVENTORY]">
            <th pFrozenColumn pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="checkbox-col cursor-default" scope="col">
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
          </ng-container>
          <ng-container *ngFor="let col of columns; trackBy: trackByColumnFn">
            <th pFrozenColumn pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="small-col cursor-default img-col" *ngIf="col.disable && col.name === 'Image'" scope="col">Image</th>
            <th
              pFrozenColumn
              pResizableColumn
              pReorderableColumn
              [pReorderableColumnDisabled]="true"
              class="small-col stock-col"
              *ngIf="col.disable && col.name === 'Stock'"
              [pSortableColumn]="col?.shortingKey"
              scope="col"
            >
              Stock <p-sortIcon [field]="col?.shortingKey"> </p-sortIcon>
            </th>
            <th pResizableColumn class="associate-col cursor-default" *ngIf="col.disable && col.name === 'Associated Stocks'" scope="col">
              Associated Stocks
            </th>
            <th pResizableColumn [pSortableColumn]="col?.shortingKey" pReorderableColumn *ngIf="!col.disable">
              {{ col.name }}
              <span *ngIf="col.name !== this.hideFieldForSearch && col.shorting">
                <p-sortIcon [field]="col.shortingKey || col.field"></p-sortIcon>
              </span>
            </th>
            <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="actions cursor-default" *ngIf="col.disable && col.name === 'Internet Groups'">
              {{ col.name }}
            </th>
            <th pResizableColumn pReorderableColumn [pReorderableColumnDisabled]="true" class="actions cursor-default" *ngIf="col.disable && col.name === 'Action'">
              {{ col.name }}
            </th>
          </ng-container>
        </tr>
        <tr class="inventory-search-tr">
          <ng-container *appHasPermission="[permissionActions.DELETE_INVENTORY || permissionActions.UPDATE_INVENTORY]">
            <th pFrozenColumn class="checkbox-col"></th>
          </ng-container>
          <ng-container *ngFor="let col of columns; trackBy: trackByColumnFn">
            <th pFrozenColumn pResizableColumn *ngIf="col.disable && col.name === 'Image'" scope="col" class="img-col"></th>
            <th pFrozenColumn pResizableColumn class="small-col stock-col" *ngIf="col.disable && col.name === 'Stock'" scope="col">
              <span class="search-input clear-btn-search">
                <input pInputText type="text" class="form-control w-100" (input)="tableSearchByColumn($event.target, col)" [(ngModel)]="col.value" />
                <i class="pi pi-times cursor-pointer pe-3" *ngIf="col.value" (click)="tableSearchByColumn($event.target, col)"></i>
              </span>
            </th>
            <th pResizableColumn *ngIf="col.disable && col.name === 'Associated Stocks'" scope="col"></th>

            <th pResizableColumn *ngIf="!col.disable">
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.searchKey === 'status'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="inventoryStatusesWithCount"
                  [(ngModel)]="col.value"
                  (onChange)="tableSearchByColumn($event, col)"
                  class="border-less inventory-list"
                  optionLabel="name"
                  optionValue="name"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="col.value !== null"
                  appendTo="body"
                  [placeholder]="col.value === null ? 'Select Status' : ''"
                >
                  <ng-template pTemplate="selectedItem">
                    <div class="country-item country-item-value" *ngIf="col.value">
                      <div>{{ col.name }} selected</div>
                    </div>
                  </ng-template>
                  <ng-template let-status pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ status.name }}</div>
                      <div class="text-secondary" *ngIf="status.count >= 0">({{ status.count }})</div>
                    </div>
                  </ng-template>
                </p-dropdown>
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.isSpecificationField && categorySpecification.length">
                <p-multiSelect
                  [options]="getAllOptions(col)"
                  defaultLabel="Select {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="col.value"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="tableSearchByColumn([], col)"
                  appendTo="body"
                >
                </p-multiSelect>
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'receivingLocationIds'">
                <p-multiSelect
                  [options]="receivingLocations"
                  defaultLabel="Select {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="col.value"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="tableSearchByColumn([], col)"
                  appendTo="body"
                >
                <ng-template let-location pTemplate="item">
                  <div class="country-item d-flex align-items-center justify-content-between w-100">
                    <div>{{ location.name }}</div>
                    <div class="text-secondary">({{ location.count }})</div>
                  </div>
                </ng-template>
                <ng-template pTemplate="empty">
                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: receivingLocations
                      }"></ng-container>
                </ng-template>
                </p-multiSelect>
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'currentLocationIds'">
                <p-multiSelect
                  [options]="currentLocations"
                  defaultLabel="Select {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="col.value"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="tableSearchByColumn([], col)"
                  appendTo="body"
                >
                <ng-template let-location pTemplate="item">
                  <div class="country-item d-flex align-items-center justify-content-between w-100">
                    <div>{{ location.name }}</div>
                    <div class="text-secondary">({{ location.count }})</div>
                  </div>
                </ng-template>
                <ng-template pTemplate="empty">
                  <ng-container [ngTemplateOutlet]="emptyMessage" [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: currentLocations
                      }"></ng-container>
                </ng-template>
                </p-multiSelect>
              </span>
              <span class="search-input" *ngIf="col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'salesPersonIds'">
                <p-multiSelect
                  [options]="salesPersonOptions"
                  defaultLabel="Select {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="col.value"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="tableSearchByColumn([], col)"
                  appendTo="body"
                >
                <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: salesPersonOptions
                      }"
                    ></ng-container>
                  </ng-template>
                  <ng-template let-salesPerson pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ salesPerson.name }}</div>
                      <div class="text-secondary">({{ salesPerson.count }})</div>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </span>
              <span class="search-input" *ngIf="col.type === 'BUTTON' && col.searchKey === 'isDisplayOnWeb'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="displayOnWebOptions"
                  [(ngModel)]="col.value"
                  (onChange)="tableSearchByColumn($event, col)"
                  class="normal-dropdowns"
                  optionLabel="name"
                  optionValue="value"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="col.value !== null"
                  appendTo="body"
                  [placeholder]="col.value === null ? 'Select an option' : ''"
                >
                <ng-template pTemplate="selectedItem">
                  <div class="country-item country-item-value" *ngIf="col.value !== null">
                    <div>{{ col.value ? 'Yes' : 'No' }}</div>
                  </div>
                </ng-template>
                  <ng-template let-item pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ item.name }}</div>
                      <div class="text-secondary">({{ item.count }})</div>
                    </div>
                  </ng-template>
                </p-dropdown>
              </span>
              <span class="search-input" *ngIf="col.type === 'DROP_DOWN' && col.searchKey === 'categoryId' && hideFieldForSearch !== 'Category'">
                <p-dropdown
                  appPreventClearFilter
                  [options]="categoryTypesOptions"
                  [(ngModel)]="col.value"
                  (onChange)="tableSearchByColumn($event, col)"
                  class="normal-dropdowns"
                  (onHide)="selectedCategoryId = col.value"
                  (onClear)="selectedCategoryId = undefined"
                  optionLabel="name"
                  optionValue="id"
                  [filter]="true"
                  filterBy="name"
                  [showClear]="col.value !== null"
                  appendTo="body"
                  [placeholder]="col.value ? '' :  'Select Category'"
                >
                <ng-template pTemplate="selectedItem">
                  <div class="country-item country-item-value" *ngIf="col.value">
                    <div>{{ col.name }} selected</div>
                  </div>
                </ng-template>
                  <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: categoryTypesOptions
                      }"
                    ></ng-container>
                  </ng-template>
                  <ng-template let-category pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ category.name }}</div>
                      <div class="text-secondary">({{ category.count }})</div>
                    </div>
                  </ng-template>
                </p-dropdown>
              </span>

              <span class="search-input" *ngIf="col.searchKey === 'makeIds'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="makes"
                  defaultLabel="Select a Make"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  selectedItemsLabel="{{ col.name }} selected"
                  [(ngModel)]="makeIds"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="clearMakes(col)"
                  appendTo="body"
                >
                  <ng-template let-make pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ make.name }}</div>
                      <div class="text-secondary">({{ make.count }})</div>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: makes
                      }"
                    ></ng-container>
                  </ng-template>
                </p-multiSelect>
              </span>

              <span class="search-input" *ngIf="col.searchKey === 'unitModelIds'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="models"
                  defaultLabel="Select a Model"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [(ngModel)]="modelIds"
                  [showClear]="true"
                  (onClear)="clearModels(col)"
                  appendTo="body"
                >
                  <ng-template let-model pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ model.name }}</div>
                      <div class="text-secondary">({{ model.count }})</div>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: models
                      }"
                    ></ng-container>
                  </ng-template>
                </p-multiSelect>
              </span>

              <span class="search-input" *ngIf="col.searchKey === 'unitTypeIds'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="unitTypes"
                  defaultLabel="Select a Unit Type"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="unitTypeIds"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="clearUnitTypes(col)"
                  appendTo="body"
                >
                  <ng-template let-unitType pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ unitType.name }}</div>
                      <div class="text-secondary">({{ unitType.count }})</div>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: unitTypes
                      }"
                    ></ng-container>
                  </ng-template>
                </p-multiSelect>
              </span>

              <span class="search-input" *ngIf="col.searchKey === 'createdByIds'">
                <p-multiSelect
                  appPreventClearFilter
                  [options]="createdByUsers"
                  defaultLabel="Select User"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="0"
                  [(ngModel)]="userIds"
                  selectedItemsLabel="{{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="clearUsers(col)"
                  appendTo="body"
                >
                  <ng-template let-user pTemplate="item">
                    <div class="country-item d-flex align-items-center justify-content-between w-100">
                      <div>{{ user.name }}</div>
                      <div class="text-secondary">({{ user.count }})</div>
                    </div>
                  </ng-template>
                  <ng-template pTemplate="empty">
                    <ng-container
                      [ngTemplateOutlet]="emptyMessage"
                      [ngTemplateOutletContext]="{
                        loader: isLoadingData,
                        data: createdByUsers
                      }"
                    ></ng-container>
                  </ng-template>
                </p-multiSelect>
              </span>

              <span class="search-input clear-btn-search" *ngIf="col.type === 'STRING' && col.name !== this.hideFieldForSearch">
                <input pInputText type="text" class="form-control w-100" [(ngModel)]="col.value" (input)="tableSearchByColumn($event.target, col)" />
                <i class="pi pi-times cursor-pointer pe-3" *ngIf="col.value" (click)="tableSearchByColumn($event.target, col)"></i>
              </span>

              <span class="search-input" *ngIf="col.type === 'INTEGER' && col.searchKey === 'year' && col.name !== this.hideFieldForSearch">
                <div class="range-filter-trigger" (click)="openYearRangeModal()">
                  <input 
                  type="text" 
                  class="form-control" 
                  [value]="getYearRangeDisplayText()" 
                  placeholder="Select Year Range" 
                  readonly>
                  <i class="pi pi-filter cursor-pointer"></i>
                </div>
              </span>

              <span class="search-input" *ngIf="col.type === 'INTEGER' && col.searchKey === 'odometerReading' && col.name !== this.hideFieldForSearch">
                <div class="range-filter-trigger" (click)="openOdometerRangeModal()">
                  <input
                    type="text"
                    class="form-control"
                    [value]="getOdometerRangeDisplayText()"
                    placeholder="Select Odometer Range"
                    readonly>
                  <i class="pi pi-filter cursor-pointer"></i>
                </div>
              </span>

              <span class="search-input" *ngIf="col.type === 'DOUBLE' && (col.searchKey === 'retailAskingPrice' || col.searchKey === 'initialInvestment' || col.searchKey === 'actualInvestment' || col.searchKey === 'totalProjectedInvestment') && col.name !== this.hideFieldForSearch">
                <div class="range-filter-trigger" (click)="openPriceRangeModal(col.searchKey)">
                  <input
                    type="text"
                    class="form-control"
                    [value]="getPriceRangeDisplayText(col.searchKey)"
                    placeholder="Select Price Range"
                    readonly>
                  <i class="pi pi-filter cursor-pointer"></i>
                </div>
              </span>

              <span class="search-input clear-btn-search" *ngIf="col.type === 'INTEGER' && col.searchKey !== 'year' && col.searchKey !== 'odometerReading' && col.name !== this.hideFieldForSearch">
                <input pInputText type="number" class="form-control w-100" [(ngModel)]="col.value" (input)="tableSearchByColumn($event.target, col)" />
                <i class="pi pi-times cursor-pointer pe-3" *ngIf="col.value" (click)="tableSearchByColumn($event.target, col)"></i>
              </span>

              <span class="search-input clear-btn-search" *ngIf="col.type === 'DOUBLE' && col.searchKey !== 'retailAskingPrice' && col.searchKey !== 'initialInvestment' && col.searchKey !== 'actualInvestment' && col.searchKey !== 'totalProjectedInvestment' && col.name !== this.hideFieldForSearch">
                <input pInputText type="number" class="form-control w-100" [(ngModel)]="col.value" (input)="tableSearchByColumn($event.target, col)" />
                <i class="pi pi-times cursor-pointer pe-3" *ngIf="col.value" (click)="tableSearchByColumn($event.target, col)"></i>
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE' && col.key === 'createdDate' && col.name !== hideFieldForSearch">
                <p-calendar
                  appendTo="body"
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  [(ngModel)]="createdDate"
                  inputId="startDateIcon"
                  (onSelect)="tableSearchByColumn($event, col)"
                  (onClearClick)="clearDate(col)"
                ></p-calendar>
              </span>
              <span class="search-input" *ngIf="col.type === 'DATE' && col.key === 'financial.purchaseDate' && col.name !== hideFieldForSearch">
                <p-calendar
                  appendTo="body"
                  [showIcon]="true"
                  [showButtonBar]="true"
                  [readonlyInput]="true"
                  [(ngModel)]="purchaseDate"
                  inputId="purchaseDateIcon"
                  (onSelect)="tableSearchByColumn($event, col)"
                  (onClearClick)="clearDate(col)"
                ></p-calendar>
              </span>
            </th>
            <th pResizableColumn *ngIf="col.disable && col.name === 'Internet Groups' && col.type === 'MULTI_DROP_DOWN' && col.searchKey === 'internetGroupIds'" scope="col">
              <span class="search-input">
                <p-multiSelect
                  [options]="internetGroupOptions"
                  defaultLabel="Select {{ col.name }}"
                  optionLabel="name"
                  optionValue="id"
                  [maxSelectedLabels]="1"
                  [(ngModel)]="col.value"
                  selectedItemsLabel="{0} {{ col.name }} selected"
                  (onChange)="tableSearchByColumn($event, col)"
                  [showClear]="true"
                  (onClear)="tableSearchByColumn([], col)"
                  appendTo="body"
                >
                </p-multiSelect>
              </span>
            </th>
            <th pResizableColumn class="actions reset-icon" *ngIf="col.disable && col.name === 'Action'">
              <div class="d-flex align-items-center justify-content-center">
                <fa-icon
                  [icon]="faIcons.faFloppyDisk"
                  class="cursor-pointer save-preference-icon"
                  (click)="saveMyPreference()"
                  pTooltip="Save Preference"
                  tooltipPosition="top"
                  alt="save-icon"
                ></fa-icon>
                <img
                  [src]="constants.staticImages.icons.edit"
                  class="cursor-pointer ms-3"
                  *ngIf="appliedPreference.length"
                  (click)="updateMyPreference()"
                  pTooltip="Update applied Preference"
                  tooltipPosition="top"
                  alt="update-icon"
                />
              </div>
            </th>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-inventory let-columns="columns" let-rowData let-rowIndex="rowIndex">
        <tr [ngClass]="{ 'selected-row': isInventorySelected(inventory) }">
          <ng-container *appHasPermission="[permissionActions.DELETE_INVENTORY || permissionActions.UPDATE_INVENTORY]">
            <td pFrozenColumn class="checkbox-col">
              <p-tableCheckbox [value]="inventory"></p-tableCheckbox>
            </td>
          </ng-container>
          <ng-container *ngFor="let col of columns; trackBy: trackByColumnFn">
            <td *ngIf="col.name === 'Image' && col.disable" class="td-inventory-list-image img-col" (click)="showDetails(rowData)" pFrozenColumn>
              <div *ngIf="inventory?.unitImages?.fullUrl; else noImage">
                <div class="image-container">
                <img [src]="inventory?.unitImages?.fullUrl" class="inventory-list-image" *ngIf="inventory?.unitImages?.fullUrl" alt="inventory image" />
                </div>
              </div>
              <ng-template #noImage>
                <div  class="image-container">
                <img [src]="constants.staticImages.noImages" class="inventory-list-image" *ngIf="!inventory?.unitImages?.fullUrl" alt="inventory image" />
                </div>
              </ng-template>
            </td>
            <td
              pFrozenColumn
              class="view-inventory stock-col"
              *ngIf="col.name === 'Stock' && col.disable"
              [ngClass]="{
                red: inventory.generalInformation.unitStatus?.id === 2
              }"
              (click)="onViewEditClick(inventory)"
            >
              {{ getEvaluatedExpression(col.key, inventory) }}
            </td>
            <td *ngIf="col.name === 'Associated Stocks' && col.disable">
              <span *ngFor="let associate of inventory.associations; let index = index" (click)="onViewAssociation(associate?.id)">
                <span
                  class="view-inventory"
                  [ngClass]="{
                    red: inventory.generalInformation.unitStatus?.id === 2
                  }"
                >
                  {{ associate?.stockNumber }}
                </span>
                <span *ngIf="index !== inventory.associations.length - 1"> , </span>
              </span>
            </td>
            <ng-container *ngIf="col.key && col.key !== null && !col.disable && col.searchKey !== 'status' && col.type !== 'IMAGE' && col.type !== 'BUTTON'">
              <td
                [ngClass]="{
                  red: inventory.generalInformation.unitStatus?.id === 2
                }"
              >
                <span *ngIf="col.type === 'DATE'">
                  {{ getEvaluatedExpression(col.key, inventory) | date: constants.dateFormat }}
                </span>
                <span *ngIf="col.type === 'DOUBLE'">
                  <span *ngIf="getEvaluatedExpression(col.key, inventory) || getEvaluatedExpression(col.key, inventory) === 0">
                    {{ getEvaluatedExpression(col.key, inventory) | currency: 'USD' : 'symbol': '1.0-2' }}
                  </span>
                </span>
                <span *ngIf="col.type !== 'DATE' && col.type !== 'DOUBLE' && col.key !== 'odometerReading'">
                  {{ getEvaluatedExpression(col.key, inventory) }}
                </span>
                <span *ngIf="col.type === 'INTEGER' && col.key === 'odometerReading'">
                  {{ getEvaluatedExpression(col.key, inventory) | number }}
                </span>
              </td>
            </ng-container>
            <td *ngIf="col.type === 'DROP_DOWN' && col.searchKey === 'status'">
              <p-dropdown
                class="border-less inventory-list status-data"
                appendTo="body"
                [options]="inventoryStatuses"
                (click)="saveClickedInventoryStatus(inventory?.generalInformation?.unitStatus?.id)"
                optionLabel="name"
                [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_INVENTORY])"
                [(ngModel)]="inventory.generalInformation.unitStatus.id"
                optionValue="id"
                (onChange)="changeStatus(inventory?.generalInformation?.unitStatus?.id, rowData?.id, inventory)"
              >
                {{ inventory?.generalInformation?.unitStatus?.name }}
              </p-dropdown>
            </td>
            <td *ngIf="col.type === 'BUTTON'">
              <span *ngIf="col.key === 'archived'">
                <ui-switch
                  [ngModel]="!inventory.archived"
                  [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_INVENTORY])"
                  [ngClass]="{
                    sold: inventory.generalInformation.unitStatus?.id === 2
                  }"
                  [loading]="selectedInventory?.id === inventory.id && isArchiveInProgress"
                  (change)="onArchive(inventory, $event)"
                >
                  <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="isArchiveInProgress && selectedInventory?.id === inventory.id"></fa-icon>
                </ui-switch>
              </span>
              <span *ngIf="col.key === 'internetOption.displayOnWeb'">
                <ui-switch
                  [ngModel]="inventory.internetOption.displayOnWeb"
                  [disabled]="!utils.hasSubModulePermission(userPermissions, [permissionActions.UPDATE_INVENTORY])"
                  (change)="updateDisplayOnWeb($event, inventory.id)"
                >
                </ui-switch>
              </span>
            </td>
            <td class="actions p-2 internet-groups-cell" *ngIf="col.name === 'Internet Groups' && col.disable">
              <div class="internet-groups-container" *ngIf="inventory.internetGroups && inventory.internetGroups.length">
                <ng-container *ngFor="let group of inventory.internetGroups; let tagIndex = index; trackBy: trackByGroupFn">
                  <p-tag class="me-2 internet-badge tag-item" [attr.data-tag-index]="tagIndex">
                    {{ group.internetGroupName }}
                  </p-tag>
                </ng-container>
                <span
                  class="badge d-inline-flex align-items-center justify-content-center show-more-badge"
                  [pTooltip]="getRemainingBadgesTemplate(inventory.internetGroups, 2)"
                  [escape]="false"
                  [tooltipStyleClass]="'badge-tooltip'"
                  tooltipPosition="right"
                  style="display: none;"
                >
                  <fa-icon [icon]="faIcons.faEllipsisH"></fa-icon>
                </span>
              </div>
            </td>
            <td class="actions" *ngIf="col.name === 'Action' && col.disable">
              <div class="actions-content m-0 row">
                <div class="col m-auto p-2">
                  <img
                    *ngIf="inventory.generalInformation.unitStatus?.id === 3 || inventory.generalInformation.unitStatus?.id === 4"
                    [src]="constants.staticImages.icons.truck"
                    alt=""
                    class="incoming-truck"
                  />
                </div>
                <div class="col m-auto p-2">
                  <ng-container *appHasPermission="[permissionActions.UPDATE_INVENTORY]">
                    <img *ngIf="inventory.generalInformation.unitStatus?.id === 2" [src]="constants.staticImages.icons.dollar" (click)="openSoldTab(inventory)" alt="dollar-icon" />
                  </ng-container>
                </div>
                <div class="col m-auto p-2">
                  <img [src]="constants.staticImages.icons.shareIcon" alt="share" pTooltip="Share" tooltipPosition="top" class="incoming-truck" (click)="shareDetails(inventory)" />
                </div>
                <div class="col m-auto p-2">
                  <img
                    [src]="constants.staticImages.icons.download"
                    alt="download-pdf"
                    pTooltip="Download PDF"
                    tooltipPosition="top"
                    class="incoming-truck"
                    (click)="downloadPDF(inventory)"
                  />
                </div>
                <div class="col m-auto p-2">
                  <img
                    [src]="constants.staticImages.icons.clone"
                    (click)="showCloneModal(inventory)"
                    pTooltip="Duplicate Inventory"
                    alt="clone-icon"
                    *appHasPermission="[permissionActions.CREATE_INVENTORY]"
                  />
                </div>
                <div class="col m-auto p-2">
                  <img
                    [src]="constants.staticImages.icons.edit"
                    (click)="onEdit(inventory)"
                    pTooltip="Edit"
                    alt="edit-icon"
                    *appHasPermission="[permissionActions.UPDATE_INVENTORY]"
                  />
                </div>
                <div class="col m-auto p-2">
                  <ng-container *appHasPermission="[permissionActions.DELETE_INVENTORY]">
                    <img
                      [src]="constants.staticImages.icons.deleteIcon"
                      alt="delete-icon"
                      [pTooltip]="'Delete'"
                      tooltipPosition="left"
                      (click)="onDelete(inventory, $event)"
                      *ngIf="selectedTabName === 'Archive'"
                    />
                  </ng-container>
                </div>
              </div>
            </td>
          </ng-container>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <td [colSpan]="cols.length + 2" class="no-data">No data to display</td>
      </ng-template>
    </p-table>
    <app-server-pagination [paginationParams]="paginationConfig" (pageChanged)="onPageChanged($event)"> </app-server-pagination>
  </ng-template>

  <p-sidebar
    class="dealer"
    [(visible)]="showCreateModal"
    [fullScreen]="true"
    (onHide)="showCreateModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
  >
    <app-inventory-add-wrapper
      (onClose)="onAddEditPopupClose($event)"
      *ngIf="showCreateModal"
      [inventoryInfo]="selectedInventory"
      [isEditMode]="isEditMode"
      [activeIndexes]="activeIndex"
      [isViewMode]="isViewMode"
      [showSoldTabs] = "showSoldTabs"
      [showHoldTabs] = "showHoldTabs"
    >
    </app-inventory-add-wrapper>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showColumnModal"
    position="right"
    (onHide)="showColumnModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    appendTo="body"
    styleClass="p-sidebar-md"
  >
    <app-column-dropdown
      (onClose)="toggleColumnSidebar()"
      [isInventoryModule]="true"
      *ngIf="showColumnModal"
      [isModelVisible]="showColumnModal"
      [privateFilter]="userSelectedColumnsArray"
      [filterParams]="getFilterSaveParams()"
      [specificationFilterParams]="getSpecificationFilterSaveParams()"
      [columnsList]="dropDownColumnList"
      (onSubmitCheck)="callFilterApiAgain()"
    >
    </app-column-dropdown>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showInventoryToBeSoldedModal"
    (onHide)="showInventoryToBeSoldedModal = false"
    [blockScroll]="true"
    [dismissible]="false"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    position="right"
    styleClass="p-sidebar-md"
    [baseZIndex]="10000"
    appendTo="body"
  >
    <app-inventory-sold [inventoryInfo]="selectedInventory" [selectedStatus]="selectedStatus" (closeModal)="onSoldModalClose($event)"> </app-inventory-sold>
  </p-sidebar>
  
  <p-sidebar
    class="dealer"
    [(visible)]="showInventoryToBeHoldModal"
    (onHide)="showInventoryToBeHoldModal = false"
    [blockScroll]="true"
    [dismissible]="false"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    position="right"
    styleClass="p-sidebar-md"
    [baseZIndex]="10000"
    appendTo="body"
  >
    <app-inventory-hold [inventoryInfo]="selectedInventory" [selectedStatus]="selectedStatus" (closeModal)="onHoldModalClose($event)"> </app-inventory-hold>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showDuplicateInventoryModal"
    (onHide)="showDuplicateInventoryModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    position="right"
    [fullScreen]="true"
    [baseZIndex]="10000"
    appendTo="body"
  >
    <app-duplicate-inventory
      *ngIf="showDuplicateInventoryModal"
      [inventoryInfo]="selectedInventoryContext"
      [inventoryStatuses]="inventoryStatuses"
      (closeModal)="onDuplicateInventoryModalClose()"
      (updateInventories)="getAll()"
    >
    </app-duplicate-inventory>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showInventoryDetails"
    (onHide)="showInventoryDetails = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    position="right"
    [fullScreen]="true"
    [baseZIndex]="10000"
    appendTo="body"
  >
    <app-view-inventory-details
      *ngIf="showInventoryDetails"
      (onClose)="showInventoryDetails = false"
      (onEdit)="editFromDetails()"
      [selectedInventory]="selectedInventory"
    ></app-view-inventory-details>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showHistoryModal"
    position="right"
    (onHide)="showHistoryModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    appendTo="body"
    styleClass="p-sidebar-md"
  >
    <app-recent-activity *ngIf="showHistoryModal" (onClose)="toggleHistorySidebar()" [moduleName]="historyModuleName"></app-recent-activity>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showPreferenceModal"
    position="right"
    (onHide)="showPreferenceModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    appendTo="body"
    styleClass="p-sidebar-md"
  >
    <app-save-preference
      *ngIf="showPreferenceModal"
      [selectedTabName]="selectedTabName"
      [myPreference]="myPreference"
      [currentUserId]="currentUser?.id ?? null"
      (onClose)="togglePreferenceModal()"
    ></app-save-preference>
  </p-sidebar>

  <p-sidebar
    class="dealer"
    [(visible)]="showPreferenceListModal"
    position="right"
    (onHide)="showPreferenceListModal = false"
    [blockScroll]="true"
    [transitionOptions]="modalTransition"
    [showCloseIcon]="false"
    appendTo="body"
    styleClass="p-sidebar-md"
  >
    <app-preference-list
      *ngIf="showPreferenceListModal"
      [currentUserId]="currentUser?.id ?? null"
      (onApplyPreference)="applyPreference($event)"
      (onDeletePreference)="deletePreference($event)"
      (onClose)="togglePreferenceListModal()"
    ></app-preference-list>
  </p-sidebar>

  <p-confirmDialog appClickOutside (clickOutside)="onBackdropClick($event)"></p-confirmDialog>

  <p-dialog
    [(visible)]="showOdometerRangeModal"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    header="Select Odometer Range"
    styleClass="range-filter-modal"
    appendTo="body">
    <div class="range-modal-content">
      <div class="row">
        <div class="col-md-6">
          <label>From (Miles)</label>
          <p-inputNumber
            [(ngModel)]="tempOdometerRangeStart"
            placeholder="Start Miles"
            styleClass="w-100"
            (onInput)="validateOdometerRange()">
          </p-inputNumber>
        </div>
        <div class="col-md-6">
          <label>To (Miles)</label>
          <p-inputNumber
            [(ngModel)]="tempOdometerRangeEnd"
            placeholder="End Miles"
            styleClass="w-100"
            (onInput)="validateOdometerRange()">
          </p-inputNumber>
        </div>
      </div>
      <div class="row" *ngIf="odometerRangeError">
        <div class="col-12">
          <small class="text-danger">{{ odometerRangeError }}</small>
        </div>
      </div>
    </div>
    <ng-template pTemplate="footer">
      <div class="modal-footer-buttons">
        <button type="button" class="btn btn-outline-secondary me-2" (click)="cancelOdometerRangeModal()">Cancel</button>
        <button type="button" class="btn btn-secondary me-2" (click)="clearOdometerRangeModal()">Clear</button>
        <button type="button" class="btn btn-primary" (click)="applyOdometerRangeModal()">Apply</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog
    [(visible)]="showPriceRangeModal"
    [modal]="true"
    [closable]="true"
    [draggable]="false"
    [resizable]="false"
    [header]="getPriceModalHeader()"
    styleClass="range-filter-modal"
    appendTo="body">
    <div class="range-modal-content">
      <div class="row">
        <div class="col-md-6">
          <label>From</label>
          <p-inputNumber
            [(ngModel)]="tempPriceRangeStart"
            mode="currency"
            currency="USD"
            locale="en-US"
            placeholder="Min Price"
            styleClass="w-100"
            (onInput)="validatePriceRange()">
          </p-inputNumber>
        </div>
        <div class="col-md-6">
          <label>To</label>
          <p-inputNumber
            [(ngModel)]="tempPriceRangeEnd"
            mode="currency"
            currency="USD"
            locale="en-US"
            placeholder="Max Price"
            styleClass="w-100"
            (onInput)="validatePriceRange()">
          </p-inputNumber>
        </div>
      </div>
      <div class="row" *ngIf="priceRangeError">
        <div class="col-12">
          <small class="text-danger">{{ priceRangeError }}</small>
        </div>
      </div>
    </div>
    <ng-template pTemplate="footer">
      <div class="modal-footer-buttons">
        <button type="button" class="btn btn-outline-secondary me-2" (click)="cancelPriceRangeModal()">Cancel</button>
        <button type="button" class="btn btn-secondary me-2" (click)="clearPriceRangeModal()">Clear</button>
        <button type="button" class="btn btn-primary" (click)="applyPriceRangeModal()">Apply</button>
      </div>
    </ng-template>
  </p-dialog>

  <p-dialog header="Select Year Range" [(visible)]="showYearRangeModal" [modal]="true" [closable]="true"
  [draggable]="false" [resizable]="false" styleClass="range-filter-modal" appendTo="body">
  <div class="range-modal-content">
    <div class="row">
      <div class="col-md-6">
        <label>From Year</label>
        <p-calendar [(ngModel)]="tempYearRangeStart" view="year" dateFormat="yy" [showIcon]="true"
          placeholder="Select start year" appendTo="body" (onSelect)="validateYearRange()">
        </p-calendar>
      </div>
      <div class="col-md-6">
        <label>To Year</label>
        <p-calendar [(ngModel)]="tempYearRangeEnd" view="year" dateFormat="yy" [showIcon]="true"
          placeholder="Select end year" appendTo="body" (onSelect)="validateYearRange()">
        </p-calendar>
      </div>
    </div>
    <div class="row" *ngIf="yearRangeError">
      <div class="col-12">
        <small class="text-danger">{{ yearRangeError }}</small>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="modal-footer-buttons">
      <button type="button" class="btn btn-secondary me-2" (click)="showYearRangeModal = false">
        Cancel
      </button>
      <button type="button" class="btn btn-outline-secondary me-2" (click)="clearYearRangeModal()">
        <i class="fa fa-trash me-2"></i>Clear
      </button>
      <button type="button" class="btn btn-primary" (click)="applyYearRangeModal()">
        Apply
      </button>
    </div>
  </ng-template>
</p-dialog>

  <ng-template #emptyMessage let-loader="loader" let-data="data">
    <div class="d-flex justify-content-center">
      <fa-icon [icon]="faIcons.faSpinner" [spin]="true" *ngIf="loader"></fa-icon>
      <p *ngIf="!loader && !data?.length">No records found</p>
    </div>
  </ng-template>
</div>

<p-sidebar
  class="dealer"
  [(visible)]="showInventoryDetailsModal"
  (onHide)="showInventoryDetailsModal = false"
  [blockScroll]="true"
  [transitionOptions]="modalTransition"
  [showCloseIcon]="false"
  position="right"
  styleClass="p-sidebar-md"
  [baseZIndex]="10000"
  appendTo="body"
>
  <app-crm-customer-inventory-details *ngIf="showInventoryDetailsModal" [quotationDetails]="selectedQuotation" (saveQuotation)="onSaveQuotation()" (closeModal)="onCloseModal()">
  </app-crm-customer-inventory-details>
</p-sidebar>
